import Loader from "components/atoms/Loader/Loader";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import { Modal } from "components/atoms/Modal/Modal";
import React from "react";

export const DeleteModal = ({
  title,
  isModalOpen,
  setIsModalOpen,
  handleChange,
  confirmationText = "Are You Sure?",
  cancelText = "No",
  agreeText = "Yes",
  loader = false,
}) => {
  return (
    <Modal
      isOpen={isModalOpen}
      onClose={() => setIsModalOpen(false)}
      headingText={title}
    >
      <MDBox>
        <MDTypography className="py-4 text-center">
          {confirmationText}
        </MDTypography>
        <MDBox className="flex justify-between">
          <MDButton
            variant="outlined"
            color="dark"
            onClick={() => setIsModalOpen(false)}
            sx={{ width: "6rem" }}
          >
            {cancelText}
          </MDButton>
          <MDButton
            variant="gradient"
            color="dark"
            onClick={() => handleChange()}
            disabled={loader}
            sx={{ width: "6rem" }}
          >
            {loader ? <Loader loaderColor="#fff" /> : agreeText}
          </MDButton>
        </MDBox>
      </MDBox>
    </Modal>
  );
};
