import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import BrokenPencil from "assets/images/broken-pencil.png";
import { useLocation, useNavigate } from "react-router-dom";
import MDButton from "components/atoms/MDButton";
import { Card } from "@mui/material";

export default function ErrorPage() {
  const location = useLocation();
  const navigate = useNavigate();

  const getErrorMessage = () => {
    let errorMessage;

    try {
      errorMessage = location?.state?.errorMessage
        ? JSON.parse(location?.state?.errorMessage)
        : null;
    } catch (e) {
      errorMessage = location?.state?.errorMessage || "Invalid Error Message";
    }

    if (errorMessage && errorMessage.length > 30) {
      return "Something went wrong";
    }

    return errorMessage;
  };

  const errorStatus = location?.state?.errorStatus;

  const handleGoHome = () => {
    navigate("/");
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <MDBox
      className={"flex flex-col items-center justify-center h-screen w-screen"}
      sx={{ pl: { xs: "0", xl: "250px" } }}
    >
      <Card>
        <MDBox
          className={
            "flex flex-col items-center justify-center gap-12 !bg-white"
          }
          sx={{
            width: { xs: "90vw", xl: "60vw" },
            height: { xs: "90vh", xl: "70vh" },
          }}
        >
          <MDBox
            display="flex"
            alignItems="center"
            justifyContent="center"
            width={{ xs: "250px", sm: "350px" }}
          >
            <img
              src={BrokenPencil}
              alt="404: Page Not Found"
              width={"100%"}
              height={"auto"}
            />
          </MDBox>
          <MDTypography
            variant="h1"
            fontWeight="bold"
            fontSize={{ xs: "2rem", sm: "3rem" }}
            sx={{
              wordWrap: "break-word",
              whiteSpace: "normal",
              width: "40rem",
              textAlign: "center",
            }}
          >
            {errorStatus && errorStatus + " : " + getErrorMessage()}
            {!errorStatus && "Something went wrong"}
          </MDTypography>
          <MDBox
            display="flex"
            alighItems="center"
            justifyContent="space-between"
            width={{ xs: "90%", sm: "50%" }}
            mt={5}
          >
            <MDButton variant="gradient" color="dark" onClick={handleGoBack}>
              Go Back
            </MDButton>
            <MDButton variant="gradient" color="info" onClick={handleGoHome}>
              Go Home
            </MDButton>
          </MDBox>
        </MDBox>
      </Card>
    </MDBox>
  );
}
