import React from "react";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import MDBox from "../MDBox";
import MDTypography from "../MDTypography";
import { Grid } from "@mui/material";
import { useNavigate } from "react-router-dom";

function AddNewInstituteCard() {
  const navigate = useNavigate();

  return (
    <Grid item xs={12}>
      <MDBox
        onClick={() => navigate("/create-new-institute")}
        sx={{
          px: 3,
          py: 3,
          cursor: "pointer",
          display: "flex",
          alignItems: "center",
          gap: 2,
          boxShadow: 2,
          borderRadius: 3,
          border: "1px solid #c9c9c9",
          background: "linear-gradient(135deg, #f9f9f9, #ffffff)",
          transition: "all 0.3s ease-in-out",
          "&:hover": {
            boxShadow: 4,
            transform: "scale(1.01)",
          },
        }}
      >
        <AddCircleOutlineIcon fontSize="medium" />

        <MDTypography variant="h6" fontWeight="bold">
          Add New Institute
        </MDTypography>
      </MDBox>
    </Grid>
  );
}

export default AddNewInstituteCard;
