import React from "react";
import MDBox from "../MDBox";
import logo from "assets/images/logo-eddyowl-white.png";
import { IconButton, Tooltip } from "@mui/material";
import LogoutIcon from "@mui/icons-material/Logout";
import { useNavigate } from "react-router-dom";

function FullPageHeader() {
  const navigate = useNavigate();

  return (
    <MDBox>
      {/* Logo at Top Left */}
      <MDBox sx={{ position: "absolute", top: 20, left: 20 }}>
        <img src={logo} alt="EddyOwl Logo" height={180} width={180} />
      </MDBox>

      {/* Floating Logout Button with Tooltip */}
      <Tooltip title="Logout" arrow>
        <IconButton
          sx={{
            position: "absolute",
            top: 20,
            right: 20,
            backgroundColor: "rgba(255, 255, 255, 0.1)",
            color: "white !important",
            "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.2)" },
          }}
          onClick={() => navigate("/authentication/logout")}
        >
          <LogoutIcon fontSize="large" />
        </IconButton>
      </Tooltip>
    </MDBox>
  );
}

export default FullPageHeader;
