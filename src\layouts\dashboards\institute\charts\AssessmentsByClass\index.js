/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// @mui material components
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import DataTable from "examples/Tables/DataTable";
import { useEffect, useState } from "react";

function AssessmentsByClass({ retreivedData }) {
  const [isDataEmpty, setIsDataEmpty] = useState(false);
  const [studentsData, setStudentsData] = useState(null);
  function convertData({ assessments, classes, students }) {
    const columns = [
      {
        Header: "Grade",
        accessor: "class",
        filterType: "search",
        display: true,
      },
      {
        Header: "Assignments",
        accessor: "assignments",
        filterType: "dropdown",
        display: true,
      },
      {
        Header: "Students",
        accessor: "students",
        filterType: "dropdown",
        display: true,
      },
    ];

    const rows = Array(classes.length);
    for (let i = 0; i < classes.length; i++) {
      rows[i] = {
        class: classes[i],
        assignments: assessments[i] || 0,
        students: students[i] || 0,
      };
    }

    return { columns, rows };
  }

  useEffect(() => {
    if (!retreivedData) return;
    const transformedData = convertData(retreivedData);

    if (
      transformedData &&
      (transformedData?.columns.length === 0 ||
        transformedData?.rows.length === 0)
    ) {
      setIsDataEmpty(true);
    } else {
      setStudentsData(transformedData);
      setIsDataEmpty(false);
    }
  }, [retreivedData, isDataEmpty]);

  return (
    <Card sx={{ width: "100%" }}>
      <MDBox display="flex">
        <MDBox
          display="flex"
          justifyContent="center"
          alignItems="center"
          width="4rem"
          height="4rem"
          variant="gradient"
          bgColor="success"
          color="white"
          shadow="md"
          borderRadius="xl"
          ml={3}
          mt={-2}
        >
          <Icon fontSize="medium" color="inherit">
            dataset
          </Icon>
        </MDBox>
        <MDBox sx={{ mt: 2, mb: 1, ml: 2 }}>
          <MDTypography variant="h6">Grade Details</MDTypography>
          <MDTypography
            component="div"
            variant="button"
            color="text"
            fontWeight="light"
          >
            Detailed analysis of overall grades
          </MDTypography>
        </MDBox>
      </MDBox>
      <MDBox p={2} pt={0}>
        <Grid container>
          <Grid
            item
            xs={12}
            sx={{
              minHeight: "28rem",
            }}
          >
            {studentsData && (
              <DataTable
                table={studentsData}
                isSorted="desc"
                customEntriesPerPage={5}
              />
            )}
            {isDataEmpty && (
              <MDBox
                width="100%"
                height="26rem"
                pt={2}
                pb={8}
                display="flex"
                justifyContent="center"
                alignItems="center"
              >
                <MDTypography variant="h5" fontWeight="medium">
                  Data Not Available !
                </MDTypography>
              </MDBox>
            )}
          </Grid>
        </Grid>
      </MDBox>
    </Card>
  );
}

export default AssessmentsByClass;
