/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// Material Dashboard 2 PRO React base styles
import colors from "assets/theme/base/colors";
import borders from "assets/theme/base/borders";
import typography from "assets/theme/base/typography";

// Material Dashboard 2 PRO React helper functions
import pxToRem from "assets/theme/functions/pxToRem";

const { text, white, dark, light, primary, secondary, info, success, warning, error } = colors;
const { borderRadius } = borders;
const { size, fontWeightMedium } = typography;

const chip = {
  styleOverrides: {
    root: {
      display: "inline-flex",
      alignItems: "center",
      padding: `${pxToRem(4)} ${pxToRem(8)}`,
      fontSize: size.sm,
      fontWeight: fontWeightMedium,
      borderRadius: borderRadius.md,
      lineHeight: 1.2,
      
      "&.MuiChip-filled": {
        color: white.main,
        backgroundColor: primary.main,
        
        "&.MuiChip-colorPrimary": {
          backgroundColor: primary.main,
          color: white.main,
        },
        
        "&.MuiChip-colorSecondary": {
          backgroundColor: secondary.main,
          color: white.main,
        },
        
        "&.MuiChip-colorInfo": {
          backgroundColor: info.main,
          color: white.main,
        },
        
        "&.MuiChip-colorSuccess": {
          backgroundColor: success.main,
          color: white.main,
        },
        
        "&.MuiChip-colorWarning": {
          backgroundColor: warning.main,
          color: white.main,
        },
        
        "&.MuiChip-colorError": {
          backgroundColor: error.main,
          color: white.main,
        },
      },
      
      "&.MuiChip-outlined": {
        backgroundColor: "transparent",
        border: `1px solid ${light.main}`,
        color: text.main,
        
        "&.MuiChip-colorPrimary": {
          borderColor: primary.main,
          color: primary.main,
        },
        
        "&.MuiChip-colorSecondary": {
          borderColor: secondary.main,
          color: secondary.main,
        },
        
        "&.MuiChip-colorInfo": {
          borderColor: info.main,
          color: info.main,
        },
        
        "&.MuiChip-colorSuccess": {
          borderColor: success.main,
          color: success.main,
        },
        
        "&.MuiChip-colorWarning": {
          borderColor: warning.main,
          color: warning.main,
        },
        
        "&.MuiChip-colorError": {
          borderColor: error.main,
          color: error.main,
        },
      },
      
      "&.MuiChip-clickable": {
        cursor: "pointer",
        transition: "all 150ms ease-in-out",
        
        "&:hover": {
          transform: "scale(1.02)",
        },
      },
      
      "&.MuiChip-sizeSmall": {
        padding: `${pxToRem(2)} ${pxToRem(6)}`,
        fontSize: size.xs,
        height: pxToRem(24),
      },
      
      "&.MuiChip-sizeMedium": {
        padding: `${pxToRem(4)} ${pxToRem(8)}`,
        fontSize: size.sm,
        height: pxToRem(32),
      },
    },
    
    label: {
      color: "inherit",
      padding: 0,
      fontSize: "inherit",
      fontWeight: "inherit",
    },
    
    icon: {
      color: "inherit",
      marginLeft: 0,
      marginRight: pxToRem(4),
      fontSize: `${pxToRem(16)} !important`,
      
      "&.MuiChip-iconSmall": {
        fontSize: `${pxToRem(14)} !important`,
        marginRight: pxToRem(2),
      },
    },
    
    deleteIcon: {
      color: "inherit",
      marginLeft: pxToRem(4),
      marginRight: 0,
      fontSize: `${pxToRem(16)} !important`,
      
      "&:hover": {
        color: error.main,
      },
      
      "&.MuiChip-deleteIconSmall": {
        fontSize: `${pxToRem(14)} !important`,
        marginLeft: pxToRem(2),
      },
    },
  },
};

export default chip;
