// src/routes.js or wherever your routes are defined
import React from "react";
import Classes from "layouts/dashboards/classes";
import Institute from "layouts/dashboards/institute";
import MDAvatar from "components/atoms/MDAvatar";
import Icon from "@mui/material/Icon";
import { Students } from "layouts/dashboards/students";
import { Assignments } from "layouts/dashboards/assignments";
import SettingsIcon from "@mui/icons-material/Settings";
import { Logout } from "layouts/authentication/Logout";
import AssignmentIcon from "@mui/icons-material/Assignment";
import StudentResponses from "layouts/dashboards/assignments/submission";
import StudentDashboard from "layouts/dashboards/students/dashboard";
import AssignmentsHandler from "utils/helperFunctions/AssignmentsHandler";
import MDTypography from "components/atoms/MDTypography";
import NavigateToSwitchInstitite from "utils/helperFunctions/NavigateToSwitchInstitite";
import GeneralSettings from "layouts/dashboards/settings/GeneralSettings";
import AdvancedSettings from "layouts/dashboards/settings/AdvancedSettings";
import StudentAssignments from "layouts/student/StudentAssignments";
import USER_ROLES from "utils/helperFunctions/USER_ROLES";

const Routes = (user, role) => {
  const profilePicture = user.picture ? user.picture : "";

  const STUDENT_ROUTES = [
    {
      type: "collapse",
      name: "My Assignments",
      key: "student-assignments",
      icon: <AssignmentIcon fontSize="medium" />,
      route: "/student/assignments",
      component: <StudentAssignments />,
      noCollapse: true,
    },
  ];

  const routes = [
    {
      type: "collapse",
      name: "Institute Dashboard",
      key: "",
      route: "/",
      icon: <Icon fontSize="medium">home</Icon>,
      component: <Institute />,
      noCollapse: true,
    },
    {
      type: "collapse",
      name: "Grade Dashboards",
      key: "grade-dashboard",
      icon: <Icon fontSize="medium">dashboard</Icon>,
      collapse: [
        {
          name: "Grade 6",
          key: "grade-6",
          route: "/grade-6",
          icon: (
            <MDTypography variant="body1" sx={{ color: "white !important" }}>
              6
            </MDTypography>
          ),
          component: <Classes grade={6} />,
        },
        {
          name: "Grade 7",
          key: "grade-7",
          route: "/grade-7",
          icon: (
            <MDTypography variant="body1" sx={{ color: "white !important" }}>
              7
            </MDTypography>
          ),
          component: <Classes grade={7} />,
        },
        {
          name: "Grade 8",
          key: "grade-8",
          icon: (
            <MDTypography variant="body1" sx={{ color: "white !important" }}>
              8
            </MDTypography>
          ),
          route: "/grade-8",
          component: <Classes grade={8} />,
        },
        {
          name: "Grade 9",
          key: "grade-9",
          icon: (
            <MDTypography variant="body1" sx={{ color: "white !important" }}>
              9
            </MDTypography>
          ),
          route: "/grade-9",
          component: <Classes grade={9} />,
        },
        {
          name: "Grade 10",
          key: "grade-10",
          icon: (
            <MDTypography
              variant="body1"
              sx={{ color: "white !important", ml: -0.5 }}
            >
              10
            </MDTypography>
          ),
          route: "/grade-10",
          component: <Classes grade={10} />,
        },
        {
          name: "Grade 11",
          key: "grade-11",
          icon: (
            <MDTypography
              variant="body1"
              sx={{ color: "white !important", ml: -0.5 }}
            >
              11
            </MDTypography>
          ),
          route: "/grade-11",
          component: <Classes grade={11} />,
        },
        {
          name: "Grade 12",
          key: "grade-12",
          icon: (
            <MDTypography
              variant="body1"
              sx={{ color: "white !important", ml: -0.5 }}
            >
              12
            </MDTypography>
          ),
          route: "/grade-12",
          component: <Classes grade={12} />,
        },
      ],
    },
    { type: "divider", key: "divider-1" },

    // Student Assignments route - only visible for student role
    ...(role === USER_ROLES.STUDENT_ROLE ? STUDENT_ROUTES : []),

    {
      type: "collapse",
      name: "Students",
      key: "students",
      icon: <Icon fontSize="medium">school</Icon>,
      route: "/students",
      component: <Students />,
      noCollapse: true,
      collapse: [
        {
          name: "All Students",
          key: "students",
          route: "/students",
          component: <Students />,
        },
        {
          name: "Student Dashboard",
          key: "student-dashboard",
          route: "/students/:studentId",
          component: <StudentDashboard />,
        },
      ],
    },
    {
      type: "collapse",
      name: "Assignments",
      key: "assignments",
      icon: <AssignmentIcon fontSize="medium" />,
      route: "/assignments",
      component: <Assignments />,
      noCollapse: true,
      collapse: [
        {
          name: "All Assignments",
          key: "assignments",
          route: "/assignments",
          component: <AssignmentsHandler />,
        },
        {
          name: "View Assignment",
          key: "view-assignment",
          route: "/assignments/:assignmentId",
          component: <AssignmentsHandler />,
        },
        {
          name: "View Submission",
          key: "view-submission",
          route: "/assignments/:assignmentId/submissions",
          component: <StudentResponses />,
        },
      ],
    },
    { type: "divider", key: "divider-2" },
    {
      type: "collapse",
      name: "Settings",
      key: "settings",
      icon: <SettingsIcon />,
      noCollapse: false,
      collapse: [
        {
          name: "General Settings",
          key: "general-settings",
          route: "/settings/general",
          icon: (
            <Icon
              fontSize="medium"
              sx={{ color: "white !important", ml: -0.5 }}
            >
              manage_accounts
            </Icon>
          ),
          component: <GeneralSettings />,
        },
        {
          name: "Advanced Settings",
          key: "advanced-settings",
          route: "/settings/advanced",
          icon: (
            <Icon
              fontSize="medium"
              sx={{ color: "white !important", ml: -0.5 }}
            >
              tune
            </Icon>
          ),
          component: <AdvancedSettings />,
        },
      ],
    },
    { type: "divider", key: "divider-3", requiresNoId: true },
    {
      type: "collapse",
      name: "Switch Profile",
      key: "switch-institute",
      route: "/redirct-to-switch-institute",
      icon: <Icon fontSize="medium">sync</Icon>,
      component: <NavigateToSwitchInstitite />,
      noCollapse: true,
      requiresNoId: true,
    },
    {
      type: "collapse",
      name: "Logout",
      key: "logout",
      route: "/authentication/logout",
      icon: <Icon fontSize="medium">logout</Icon>,
      noCollapse: true,
      component: <Logout />,
      requiresNoId: true,
    },
    { type: "divider", key: "divider-4", requiresNoId: true },
    {
      type: "collapse",
      name: user.name,
      key: "profile",
      icon: <MDAvatar src={profilePicture} alt={user.name} size="sm" />,
      noCollapse: true,
      requiresNoId: true,
    },
    { type: "divider", key: "divider-5", requiresNoId: true },
  ];

  return routes;
};

export default Routes;
