import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import HighchartsMore from "highcharts/highcharts-more";
import { Card } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";

HighchartsMore(Highcharts);

// Function to prepare chapter-wise data
const getChapterData = (data) => {
  return data.map((chapter) => ({
    name: chapter.chapter,
    y: chapter.score,
    drilldown: chapter.chapter,
  }));
};

// Function to prepare topic-wise data for a selected chapter
const getTopicData = (data, selectedChapter) => {
  const chapter = data.find((ch) => ch.chapter === selectedChapter);
  return (
    chapter?.topicsWithScore.map((topic) => ({
      name: topic.topic,
      y: topic.topicScore,
    })) || []
  );
};

const TopicWisePerformanceChart = ({ data }) => {
  const [chartOptions, setChartOptions] = useState(null);
  const [selectedChapter, setSelectedChapter] = useState(null);

  useEffect(() => {
    const seriesData = selectedChapter
      ? getTopicData(data, selectedChapter)
      : getChapterData(data);

    const options = {
      chart: {
        type: "line",
        polar: true,
        height: "600px",
      },
      title: {
        text: selectedChapter
          ? `Topics in ${selectedChapter}`
          : "Chapter-wise Performance",
        align: "left",
        x: 20,
      },
      subtitle: {
        text: !selectedChapter ? "Click on a chapter to view topics" : "",
        align: "left",
        x: 20,
      },
      pane: {
        size: "65%",
      },
      legend: {
        enabled: false,
      },
      xAxis: {
        categories: seriesData.map((item) => item.name),
        tickmarkPlacement: "on",
        lineWidth: 0,
        labels: {
          useHTML: true,
          formatter: function () {
            const labelName = this.value;
            if (!selectedChapter) {
              return `<span class="clickable-label">${labelName}</span>`;
            } else {
              return `<span class="chapter-label">${labelName}</span>`;
            }
          },
        },
      },
      yAxis: {
        min: 0,
        title: { text: "Score" },
      },
      series: [
        {
          name: selectedChapter ? `Topics` : `Chapters`,
          data: seriesData,
          pointPlacement: "on",
          colorByPoint: true,
        },
      ],
      tooltip: {
        outside: true,

        formatter: function () {
          return `<b>${this.point.name}</b>: <b>${this.point.y}</b>`;
        },
      },
      plotOptions: {
        series: {
          point: {
            events: {
              click: function (event) {
                if (!selectedChapter) {
                  setSelectedChapter(event.point.name);
                }
              },
            },
          },
        },
      },
      credits: {
        enabled: false,
      },
    };

    setChartOptions(options);
  }, [selectedChapter]);

  // Handle label clicks only in the chapter view
  useEffect(() => {
    const handleLabelClick = (event) => {
      if (
        !selectedChapter &&
        event.target.classList.contains("clickable-label")
      ) {
        setSelectedChapter(event.target.textContent);
      }
    };

    document.addEventListener("click", handleLabelClick);
    return () => document.removeEventListener("click", handleLabelClick);
  }, [selectedChapter]);

  return (
    <Card>
      <MDBox sx={{ position: "relative", py: 2 }}>
        {selectedChapter && (
          <MDBox
            sx={{
              position: "absolute",
              top: 70,
              left: 20,
              zIndex: 50,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              width: "93%",
              mt: 2,
            }}
          >
            <MDBox
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                cursor: "pointer",
              }}
              onClick={() => setSelectedChapter(null)}
            >
              <ArrowBackIcon color="warning" fontSize="medium" />
              <MDTypography variant="h6" color="warning">
                Back
              </MDTypography>
            </MDBox>
            <MDBox
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                cursor: "pointer",
              }}
              onClick={() => setSelectedChapter(null)}
            >
              <RestartAltIcon color="primary" fontSize="medium" />
              <MDTypography variant="h6" color="primary">
                Reset
              </MDTypography>
            </MDBox>
          </MDBox>
        )}
        {chartOptions && (
          <HighchartsReact highcharts={Highcharts} options={chartOptions} />
        )}
      </MDBox>
    </Card>
  );
};

export default TopicWisePerformanceChart;
