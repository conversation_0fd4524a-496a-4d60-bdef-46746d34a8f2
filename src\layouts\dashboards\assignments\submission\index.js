import React, { useState, useContext, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import Dashboard<PERSON>avbar from "examples/Navbars/DashboardNavbar";
import MDBox from "components/atoms/MDBox";
import Loader from "components/atoms/Loader/Loader";
import { ApiServiceContext } from "context";
import PropTypes from "prop-types";
import Card from "@mui/material/Card";
import PerformanceCharts from "components/molecules/PerformanceChart/PerformanceChart";
import { useSubmissionData } from "context/useSubmissionData";
import { useImageUrls } from "context/useImageUrl";
import MDButton from "components/atoms/MDButton";
import PdfGenerator from "components/atoms/PdfGenerator/PdfGenerator";
import StudentResponseForm from "components/organisms/viewSubmission/StudentResponseForm/StudentResponseForm";
import MDTypography from "components/atoms/MDTypography";
import { UploadGradeModal } from "components/molecules/UploadGradeModal/UploadGradeModal";
import { DeleteModal } from "components/molecules/DeleteModal/DeleteModal";
import SUBMISSION_STATUS from "utils/helperFunctions/SUBMISSION_STATUS";
import CancelIcon from "@mui/icons-material/Cancel";
import HourglassTopIcon from "@mui/icons-material/HourglassTop";
import USER_ROLES from "utils/helperFunctions/USER_ROLES";

export function StudentResponses({ viewOnly = true }) {
  const navigate = useNavigate();

  const [editMode, setEditMode] = useState(false);
  const [apiProcessing, setApiProcessing] = useState(false);
  const [formKey, setFormKey] = useState(0);
  const { apiService } = useContext(ApiServiceContext);
  const { assignmentId } = useParams();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const submissionId = queryParams.get("studentId");
  const [initialValues, setInitialValues] = useState({});
  const [downloadPDF, setDownloadPDF] = useState(false);

  const [isGradeModalOpen, setIsGradeModalOpen] = useState(false);
  const [selectedStudentId, setSelectedStudentId] = useState(null);
  const [isUploadModalLoading, setIsUploadModalLoading] = useState(false);

  // for implementing next & previous button
  const [allStudents, setAllStudents] = useState([]);
  const [responseFormStatus, setResponseFormStatus] = useState(null);
  const [currentStudent, setCurrentStudent] = useState(null);

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [currentStudentIdx, setCurrentStudentIdx] = useState(0);

  const { submissionData, submissionLoading, refetchSubmissionData } =
    useSubmissionData(submissionId, assignmentId);

  const onPdfRenderComplete = () => {
    setDownloadPDF(false);
  };

  const isStudent = apiService?.role === USER_ROLES.STUDENT_ROLE;

  useEffect(() => {
    if (!submissionData) return;

    // Get the last status from history
    const lastHistoryStatus =
      submissionData?.history?.[submissionData.history.length - 1]?.status;

    // Map numeric status to string status
    let statusString;
    switch (lastHistoryStatus) {
      case 0:
        statusString = SUBMISSION_STATUS.DUE;
        break;
      case 1:
        statusString = SUBMISSION_STATUS.PROCESSING;
        break;
      case 2:
        statusString = SUBMISSION_STATUS.PROCESSING;
        break;
      case 3:
        statusString = SUBMISSION_STATUS.GRADED;
        break;
      case 4:
        statusString = SUBMISSION_STATUS.FAILED;
        break;
      case 5:
        statusString = SUBMISSION_STATUS.GRADED;
        break;
      default:
        statusString = SUBMISSION_STATUS.DUE;
    }

    // Update allStudents with the new status
    setAllStudents((prevStudents) =>
      prevStudents.map((student) =>
        student.studentId === submissionData.studentId
          ? { ...student, status: statusString }
          : student
      )
    );

    setInitialValues({
      testName: submissionData?.assignmentName,
      totalScore: submissionData?.assignmentScore,
      totalAchievedScore: submissionData?.totalScore,
      imageIdList: submissionData?.imageIds,
      StudentResponseList: submissionData?.studentResponses?.map(
        (response) => ({
          questionNumber: response.questionNumber,
          question: response.question,
          studentResponse: response.response,
          score: response.score ?? 0,
          feedback: response.feedback,
          questionScore: response.questionScore,
          questionRubric: response.rubric,
          topics: response.topics,
        })
      ),
      missedQuestionList: submissionData?.missedQuestions,
      performance: submissionData?.chapterPerformance,
      class: submissionData?.studentGrade,
      section: submissionData?.studentSection,
      studentName: `${submissionData?.studentFirstName} ${submissionData?.studentLastName}`,
      rollNumber: submissionData?.studentRollNumber,
      studentId: submissionData?.studentId,
      name: `${submissionData?.studentFirstName} ${submissionData?.studentLastName}`,
    });

    setCurrentStudent({
      class: submissionData?.studentGrade,
      section: submissionData?.studentSection,
      status: statusString,
      studentId: submissionData?.studentId,
      studentName: `${submissionData?.studentFirstName} ${submissionData?.studentLastName}`,
      studentRollNumber: submissionData?.studentRollNumber,
    });

    setSelectedStudentId(submissionData?.studentId);
  }, [submissionData]);

  const imageUrls = useImageUrls(initialValues?.imageIdList);
  const handleCancel = () => {
    setEditMode(false);
    setFormKey((prevKey) => prevKey + 1);
  };

  const validateTotalScore = (values) => {
    const totalQuestionScore = values?.StudentResponseList?.reduce(
      (sum, question) => sum + Number(question.score),
      0
    );
    return totalQuestionScore <= values.totalScore;
  };

  const handleSave = async (values, actions) => {
    try {
      if (!validateTotalScore(values)) {
        actions.setSubmitting(false);
        toast.error("The total question score exceeds the total score.");
        setFormKey((prevKey) => prevKey + 1);
        return;
      }
      setApiProcessing(true);
      const submissionData = {
        studentResponses: values?.StudentResponseList?.map((response) => ({
          questionNumber: response.questionNumber,
          studentResponse: response.studentResponse,
          score: response.score,
          feedback: response.feedback,
        })),
      };

      const res = await apiService.editSubmissions(
        assignmentId,
        initialValues.studentId,
        submissionData
      );

      if (!res) {
        toast.error("Error updating submission");
        setEditMode(false);
        setApiProcessing(false);
        setFormKey((prevKey) => prevKey + 1);
        return;
      }

      setInitialValues((prevValues) => ({
        ...prevValues,
        totalScore: res?.totalScore,
        StudentResponseList: res?.StudentResponseList,
      }));

      await refetchSubmissionData();

      toast.success("Submission updated successfully");
    } catch (err) {
      toast.error("Error updating submission");
    } finally {
      setEditMode(false);
      setApiProcessing(false);
      setFormKey((prevKey) => prevKey + 1);
    }
  };

  const handleFormStateChange = (student) => {
    const validStatuses = Object.values(SUBMISSION_STATUS);

    if (validStatuses.includes(student.status)) {
      setResponseFormStatus(student.status);
      editMode && setEditMode(false);
      navigate(
        `/assignments/${assignmentId}/submissions?studentId=${student.studentId}`
      );
    }
  };

  const handleNext = () => {
    const currentIndex = allStudents.findIndex(
      (student) => student.studentId === currentStudent.studentId
    );

    setCurrentStudentIdx(currentIndex + 1);

    if (currentIndex < allStudents.length - 1) {
      const nextStudent = allStudents[currentIndex + 1];
      setCurrentStudent(nextStudent);
      setSelectedStudentId(nextStudent.studentId);
      handleFormStateChange(nextStudent);
    }
  };
  const handlePrevious = () => {
    const currentIndex = allStudents.findIndex(
      (student) => student.studentId === currentStudent.studentId
    );

    setCurrentStudentIdx(currentIndex - 1);

    if (currentIndex > 0) {
      const previousStudent = allStudents[currentIndex - 1];
      setCurrentStudent(previousStudent);
      setSelectedStudentId(previousStudent.studentId);
      handleFormStateChange(previousStudent);
    }
  };

  const fetchStudents = async (assignmentId) => {
    try {
      const result = await apiService.getStudentAsPerExam(assignmentId);
      setAllStudents(result);
    } catch (err) {
      toast.error("Error fetching students");
    }
  };

  useEffect(() => {
    if (!allStudents?.length > 0 || !currentStudent?.studentId) return;

    setCurrentStudentIdx(
      allStudents?.findIndex(
        (student) => student.studentId === currentStudent.studentId
      )
    );
  }, [allStudents, currentStudent]);

  useEffect(() => {
    if (allStudents.length > 0) {
      const currentStudent = allStudents.find(
        (student) => student.studentId === submissionId
      );

      setCurrentStudent(currentStudent);

      setSelectedStudentId(currentStudent.studentId);
      setResponseFormStatus(currentStudent.status);
    }
  }, [allStudents, submissionId]);

  useEffect(() => {
    // Skip fetching all students for student role
    if (!isStudent) {
      fetchStudents(assignmentId);
    } else {
      // For student role, just set the current student and status
      setCurrentStudent({ studentId: submissionId });
      setSelectedStudentId(submissionId);
      // Set response form status to GRADED for students
      setResponseFormStatus(SUBMISSION_STATUS.GRADED);
    }
  }, [assignmentId, apiService, isStudent, submissionId]);

  // Only run this effect for non-student roles
  useEffect(() => {
    if (!isStudent && allStudents.length > 0) {
      const currentStudent = allStudents.find(
        (student) => student.studentId === submissionId
      );

      if (currentStudent) {
        setCurrentStudent(currentStudent);
        setSelectedStudentId(currentStudent.studentId);
        setResponseFormStatus(currentStudent.status);
      }
    }
  }, [allStudents, submissionId, isStudent]);

  // Only run this effect for non-student roles
  useEffect(() => {
    if (!isStudent && (!allStudents?.length > 0 || !currentStudent?.studentId))
      return;

    setCurrentStudentIdx(
      allStudents?.findIndex(
        (student) => student.studentId === currentStudent.studentId
      )
    );
  }, [allStudents, currentStudent, isStudent]);

  const handleDeleteSubmission = async () => {
    setIsDeleteLoading(true);
    try {
      const result = await apiService.deleteStudentSubmission(
        assignmentId,
        currentStudent.studentId
      );

      if (!result) {
        toast.error("Submission deletion failed");
        return;
      }

      setIsDeleteModalOpen(false);

      setAllStudents((prevStudents) =>
        prevStudents.map((student) =>
          student.studentId === selectedStudentId
            ? { ...student, status: SUBMISSION_STATUS.DUE }
            : student
        )
      );

      setResponseFormStatus(SUBMISSION_STATUS.DUE);
    } catch (err) {
      toast.error("Error deleting submission");
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const handleDeleteSubmissionModalOpen = () => {
    setIsDeleteModalOpen(true);
  };

  // for Redirect user to the grade page when the user navigates back
  useEffect(() => {
    const handleBackNavigation = () => {
      navigate(`/assignments/${assignmentId}?operation=grade`, {
        replace: true,
      });
    };

    // Listen for the popstate event (browser back button)
    window.addEventListener("popstate", handleBackNavigation);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener("popstate", handleBackNavigation);
    };
  }, [navigate, assignmentId]);

  // Modify the loading check
  if (
    submissionLoading ||
    ((responseFormStatus === SUBMISSION_STATUS.GRADED ||
      responseFormStatus === SUBMISSION_STATUS.PUBLISHED) &&
      !initialValues) ||
    responseFormStatus === null
  ) {
    return <Loader fullScreen message="Don't reload the page" />;
  }

  return (
    <DashboardLayout>
      <DashboardNavbar breadCrumbText={initialValues?.testName} />
      {!isStudent && (
        <DeleteModal
          isModalOpen={isDeleteModalOpen}
          setIsModalOpen={setIsDeleteModalOpen}
          handleChange={handleDeleteSubmission}
          title="Delete Submission"
          loader={isDeleteLoading}
        />
      )}
      <MDBox py={1}>
        <MDBox mb={3}>
          <MDBox
            display="flex"
            flexDirection={{ xs: "column", sm: "row" }}
            alignItems="start"
            justifyContent={{ xs: "space-between", sm: "initial" }}
            p={1}
            gap={{ xs: 1, sm: 3 }}
            ml={1}
            mb={{ xs: 8, lg: 2 }}
            mt={-1}
          >
            <MDBox display="flex" alignItems="center" gap={1}>
              <MDTypography
                variant="h5"
                fontWeight="regular"
                sx={{ textWrap: "nowrap" }}
              >
                Student:
              </MDTypography>
              <MDTypography
                variant="h5"
                fontWeight="medium"
                sx={{ textWrap: "word-break" }}
              >
                {currentStudent?.studentName}
              </MDTypography>
            </MDBox>
            <MDBox display="flex" alignItems="center" gap={1}>
              <MDTypography
                variant="h5"
                fontWeight="regular"
                sx={{ textWrap: "nowrap" }}
              >
                Grade:
              </MDTypography>
              <MDTypography
                variant="h5"
                fontWeight="medium"
                sx={{ textWrap: "word-break" }}
              >
                {`${currentStudent?.class} (${currentStudent?.section})`}
              </MDTypography>
            </MDBox>
            <MDBox display="flex" alignItems="center" gap={1}>
              <MDTypography
                variant="h5"
                fontWeight="regular"
                sx={{ textWrap: "nowrap" }}
              >
                Roll No:
              </MDTypography>
              <MDTypography
                variant="h5"
                fontWeight="medium"
                sx={{ textWrap: "word-break" }}
              >
                {currentStudent?.studentRollNumber}
              </MDTypography>
            </MDBox>
          </MDBox>
          <Card
            sx={{
              padding: "20px",
              position: "relative",
              paddingBottom: "2rem",
              minHeight: "calc(100vh - 210px)",
            }}
          >
            {!isStudent && (
              <MDBox
                display="flex"
                alighItems="center"
                gap={2}
                mt={3}
                ml={0.5}
                sx={{
                  position: "absolute",
                  top: { xs: -80, sm: -80 },
                  right: { xs: 25, lg: 20 },
                  zIndex: 1000,
                }}
              >
                <MDButton
                  variant="outlined"
                  color="dark"
                  onClick={handlePrevious}
                  disabled={currentStudentIdx === 0}
                  sx={{ width: "6rem" }}
                >
                  Previous
                </MDButton>
                <MDButton
                  variant="outlined"
                  color="dark"
                  onClick={handleNext}
                  disabled={currentStudentIdx === allStudents.length - 1}
                  sx={{ width: "6rem" }}
                >
                  Next
                </MDButton>
              </MDBox>
            )}

            {responseFormStatus === SUBMISSION_STATUS.GRADED ||
            responseFormStatus === SUBMISSION_STATUS.PUBLISHED ? (
              <StudentResponseForm
                initialValues={initialValues}
                editMode={editMode}
                formKey={formKey}
                handleSave={handleSave}
                imageUrls={imageUrls}
                downloadPDF={downloadPDF}
                setDownloadPDF={setDownloadPDF}
                setEditMode={setEditMode}
                apiProcessing={apiProcessing}
                handleDeleteSubmissionModalOpen={
                  handleDeleteSubmissionModalOpen
                }
                handleCancel={handleCancel}
                isStudent={isStudent}
                showRubric={!isStudent}
              />
            ) : responseFormStatus === SUBMISSION_STATUS.DUE ? (
              <MDBox
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                sx={{ minHeight: "calc(100vh - 35rem)" }}
                gap={3}
              >
                <MDBox
                  sx={{
                    width: "100%",
                    height: "100%",
                  }}
                >
                  <UploadGradeModal
                    assignmentId={assignmentId}
                    selectedStudentId={selectedStudentId}
                    setSelectedStudentId={setSelectedStudentId}
                    isGradeModalOpen={isGradeModalOpen}
                    setIsGradeModalOpen={setIsGradeModalOpen}
                    setAllStudents={setAllStudents}
                    allStudents={allStudents}
                    isUploadModalLoading={isUploadModalLoading}
                    setIsUploadModalLoading={setIsUploadModalLoading}
                    isFromViewSubmission={true}
                    responseFormStatus={responseFormStatus}
                    setResponseFormStatus={setResponseFormStatus}
                  />
                </MDBox>
              </MDBox>
            ) : responseFormStatus === SUBMISSION_STATUS.FAILED ? (
              <MDBox
                mt={8}
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                sx={{ minHeight: "calc(100vh - 35rem)" }}
                gap={4}
              >
                <CancelIcon
                  color="error"
                  sx={{
                    fontSize: { xs: "3rem !important", xl: "4rem !important" },
                  }}
                />

                <MDTypography variant="h4" fontWeight="bold">
                  Submission Failed
                </MDTypography>

                <MDTypography
                  variant="h5"
                  fontWeight="regular"
                  sx={{
                    fontSize: {
                      xs: "1rem",
                      md: "1.3rem",
                    },
                  }}
                >
                  Delete the failed submission and upload a new one
                </MDTypography>

                <MDButton
                  variant="outlined"
                  color="error"
                  onClick={() => setIsDeleteModalOpen(true)}
                >
                  Delete Submission
                </MDButton>
              </MDBox>
            ) : responseFormStatus === SUBMISSION_STATUS.PROCESSING ? (
              <MDBox
                mt={8}
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                sx={{ minHeight: "calc(100vh - 35rem)" }}
                gap={4}
              >
                <HourglassTopIcon
                  color="info"
                  sx={{
                    fontSize: { xs: "3rem !important", xl: "4rem !important" },
                  }}
                />

                <MDTypography variant="h4" fontWeight="bold">
                  Submission is Processing
                </MDTypography>

                <MDTypography
                  variant="h5"
                  fontWeight="regular"
                  sx={{
                    fontSize: {
                      xs: "1rem",
                      md: "1.3rem",
                    },
                  }}
                >
                  Revisit the submission after some time to get updated one
                </MDTypography>
              </MDBox>
            ) : null}
          </Card>

          {(responseFormStatus === SUBMISSION_STATUS.GRADED ||
            responseFormStatus === SUBMISSION_STATUS.PUBLISHED) &&
            initialValues.performance && (
              <PerformanceCharts data={initialValues.performance} />
            )}
        </MDBox>
      </MDBox>
      <MDBox sx={{ display: "none" }}>
        {(responseFormStatus === SUBMISSION_STATUS.GRADED ||
          responseFormStatus === SUBMISSION_STATUS.PUBLISHED) &&
          downloadPDF && (
            <PdfGenerator
              triggerDownload={downloadPDF}
              data={initialValues}
              onRenderComplete={onPdfRenderComplete}
              isInsidePdf={true}
            />
          )}
      </MDBox>
    </DashboardLayout>
  );
}

StudentResponses.propTypes = {
  viewOnly: PropTypes.bool,
};

export default StudentResponses;
