import { TestForm } from "components/molecules/CreateTestForm/TestForm";
import { Assignments } from "layouts/dashboards/assignments";
import { Grades } from "layouts/dashboards/assignments/grade/grade";
import { useParams, useSearchParams } from "react-router-dom";

const AssignmentsHandler = () => {
  const { assignmentId } = useParams();
  const [searchParams] = useSearchParams();

  const operation = searchParams.get("operation"); // e.g., "view", "edit", "grade"

  if (operation === "new") {
    return <TestForm />;
  }

  if (!assignmentId) {
    return <Assignments />; // Fallback for list view if no assignmentId
  }

  switch (operation) {
    case "edit":
      return <TestForm formType="edit" assignmentId={assignmentId} />;
    case "view":
      return (
        <TestForm viewOnly={true} formType="view" assignmentId={assignmentId} />
      );
    case "grade":
      return <Grades assignmentId={assignmentId} />;
    default:
      return <Grades assignmentId={assignmentId} />;
  }
};

export default AssignmentsHandler;
