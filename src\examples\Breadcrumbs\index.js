/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// react-router-dom components
import { Link } from "react-router-dom";

// prop-types is a library for typechecking of props.
import PropTypes from "prop-types";

// @mui material components
import { Breadcrumbs as MuiBreadcrumbs } from "@mui/material";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import { useContext } from "react";
import { ApiServiceContext } from "context";

function Breadcrumbs({
  icon,
  title,
  route,
  light = false,
  isHome = false
}) {
  const routes = route.slice(0, -1);
  const { apiService } = useContext(ApiServiceContext);

  return (
    <MDBox mr={{ xs: 0, xl: 8 }}>
      <MuiBreadcrumbs
        sx={{
          "& .MuiBreadcrumbs-separator": {
            color: ({ palette: { white, grey } }) =>
              light ? white.main : grey[600],
          },
          marginTop: "-5px",
        }}
      >
        <Link to={apiService.instituteId == null ? "/setting" : "/"}>
          <MDTypography
            component="span"
            variant="body2"
            color={light ? "white" : "dark"}
            opacity={light ? 0.8 : 0.5}
            sx={{
              lineHeight: 0,
              whiteSpace: "nowrap", // Prevents text from wrapping
              marginTop: "0px",
            }}
          >
            <Icon>{icon}</Icon>
          </MDTypography>
        </Link>
        {routes?.map((el, index) => (
          <Link to={`/${routes.slice(0, index + 1).join("/")}`} key={el}>
            <MDTypography
              component="span"
              variant="body2"
              textTransform="capitalize"
              color={light ? "white" : "dark"}
              opacity={light ? 0.8 : 0.5}
              sx={{ lineHeight: 0 }}
            >
              {el}
            </MDTypography>
          </Link>
        ))}
        {!isHome && (
          <MDTypography
            variant="body2"
            // fontWeight="medium"
            textTransform="capitalize"
            color={light ? "white" : "dark"}
            sx={{ lineHeight: 0 }}
          >
            {title.replace("-", " ")}
          </MDTypography>
        )}
      </MuiBreadcrumbs>
    </MDBox>
  );
}

// Typechecking props for the Breadcrumbs
Breadcrumbs.propTypes = {
  icon: PropTypes.node.isRequired,
  title: PropTypes.string.isRequired,
  route: PropTypes.oneOfType([PropTypes.string, PropTypes.array]).isRequired,
  light: PropTypes.bool,
};

export default Breadcrumbs;
