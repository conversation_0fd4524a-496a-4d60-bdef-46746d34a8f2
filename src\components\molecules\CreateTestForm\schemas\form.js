const form = {
  formId: "new-test-form",
  formField: {
    name: {
      name: "name",
      label: "Assignment Name",
      type: "text",
      errorMsg: "Assignment name is required.",
    },
    totalScore: {
      name: "totalScore",
      label: "Total Score",
      type: "number",
      errorMsg: "Total Score is required.",
    },
    class: {
      name: "class",
      label: "Class",
      type: "number",
      errorMsg: "Grade is required.",
    },
    subjectName: {
      name: "subjectName",
      label: "Subject Name",
      type: "text",
      errorMsg: "Subject Name is required.",
    },
    duration: {
      name: "duration",
      label: "Duration",
      type: "number",
      errorMsg: "Duration is required.",
    },
    sectionList: {
      name: "sectionList",
      label: "Sections",
      type: "array",
      errorMsg: "At least one section is required.",
    },
    questions: {
      name: "questions",
      label: "Questions",
      type: "array",
      errorMsg: "At least one question is required.",
    },
  },
};

export default form;
