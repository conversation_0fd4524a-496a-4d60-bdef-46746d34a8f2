import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import Loader from "components/atoms/Loader/Loader";
import HC_more from "highcharts/highcharts-more";

HC_more(Highcharts);

const getTopicWisePerformanceSpiderChartOptions = (data) => {
  if (!data) return null;

  const finalDataSeries = [];

  data.forEach((chapter) => {
    chapter.topicsWithScore.forEach((topic) => {
      if (topic.topicScore !== undefined) {
        finalDataSeries.push({
          name: topic.topic,
          y: topic.topicScore * 10, // Scale scores (optional)
        });
      }
    });
  });

  if (finalDataSeries.length === 0) return null;

  const options = {
    chart: {
      type: "line",
      polar: true,
      height: "700px",
      spacing: [30, 20, 0, 20],
      borderRadius: 25,
    },
    title: {
      text: "Topic-Wise Performance",
      align: "left",
    },
    legend: {
      enabled: false,
    },
    pane: {
      size: "80%",
    },
    tooltip: {
      pointFormat: "{point.name}: <b>{point.y:.2f}</b>",
      outside: true, // Prevent overlap with labels
    },
    xAxis: {
      categories: finalDataSeries.map((data) => data.name),
      tickmarkPlacement: "on",
      lineWidth: 0,
      labels: {
        style: { fontSize: "12px" },
      },
    },
    yAxis: {
      min: 0,
      max: 100,
      title: {
        text: "Score",
      },
    },
    series: [
      {
        name: "Performance",
        data: finalDataSeries,
        colorByPoint: true,
      },
    ],
    credits: {
      enabled: false,
    },
  };

  return options;
};

const DownloadSubmissionPerformanceSpiderChart = ({ data }) => {
  const [chartOptions, setChartOptions] = useState(null);

  useEffect(() => {
    const options = getTopicWisePerformanceSpiderChartOptions(data);
    setChartOptions(options);
  }, [data]);

  return (
    <div>
      {chartOptions ? (
        <HighchartsReact highcharts={Highcharts} options={chartOptions} />
      ) : (
        <Loader />
      )}
    </div>
  );
};

export default DownloadSubmissionPerformanceSpiderChart;
