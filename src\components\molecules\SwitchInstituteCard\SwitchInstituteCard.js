import React from "react";
import { Card, Grid } from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import MDButton from "components/atoms/MDButton";
import InstituteCard from "components/atoms/InstituteCard/InstituteCard";
import InstituteActionCard from "components/atoms/InstituteActionCard/InstituteActionCard";
import Loader from "components/atoms/Loader/Loader";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";

function SwitchInstituteCard({
  institutesLoading,
  institutesList,
  handleCreateNewInstitute,
  hasStudentRole,
  onChangeRole,
}) {
  return (
    <Card
      sx={{
        p: 3,
        mb: 4,
        minHeight: "400px",
        maxHeight: "70vh",
        width: { xs: "95%", lg: "40%" },
        maxWidth: "500px",
        boxShadow: 3,
        borderRadius: "12px",
        mt: { xs: "120px", md: "60px" },
        mx: "auto",
      }}
    >
      {institutesLoading ? (
        <MDBox
          width="100%"
          height="100%"
          display="flex"
          justifyContent="center"
          alignItems="center"
        >
          <Loader message="Getting Institutes" />
        </MDBox>
      ) : (
        <>
          <MDBox
            display="flex"
            sx={{
              flexDirection: { xs: "column", sm: "row" },
              pb: 2,
            }}
            justifyContent="space-between"
          >
            <MDBox>
              <MDTypography variant="h5" mb={-1}>
                Switch Institute
              </MDTypography>
              <MDTypography variant="button" color="dark">
                Select an institute to continue
              </MDTypography>
            </MDBox>
            {hasStudentRole && (
              <MDBox sx={{ alignSelf: { xs: "flex-end", sm: "center" } }}>
                <MDButton
                  variant="gradient"
                  color="info"
                  onClick={onChangeRole}
                >
                  Change Role
                </MDButton>
              </MDBox>
            )}
          </MDBox>
          <MDBox
            sx={{
              overflowX: "hidden",
              overflowY: "auto",
              scrollbarWidth: "thin",
              scrollbarColor: "#444 #f1f1f1",
              px: 1,
              pb: 1,
            }}
          >
            <Grid container spacing={2}>
              {institutesList?.map((institute, index) => (
                <InstituteCard key={index} data={institute} />
              ))}

              <InstituteActionCard
                icon={AddCircleOutlineIcon}
                title="Add New Institute"
                onClick={handleCreateNewInstitute}
              />
            </Grid>
          </MDBox>
        </>
      )}
    </Card>
  );
}

export default SwitchInstituteCard;

