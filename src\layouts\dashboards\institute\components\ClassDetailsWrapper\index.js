import React, { useContext, useEffect, useState } from "react";
import { Grid } from "@mui/material";
import { ApiServiceContext } from "context";
import assessmentsByClassData from "../../charts/data/assessmentsByClassData";
import AssessmentsByClass from "../../charts/AssessmentsByClass";
import AssessmentByClassHorizontalBar from "../../charts/assessmentByClassHorizontalBar";
import { toast } from "react-toastify";
import ClassDetailsSkeleton from "../../SkeletonLoaders/ClassDetailsSkeleton";

const ClassDetailsWrapper = () => {
  const { apiService } = useContext(ApiServiceContext);

  const [classAssessments, setClassAssessments] = useState(null);

  useEffect(() => {
    if (classAssessments) return;
    apiService
      .getAssessmentsByClass()
      .then((data) => setClassAssessments(data))
      .catch(() => {
        return toast.error("Error loading grade assessments");
      });
  }, []);

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        {classAssessments ? (
          <AssessmentsByClass retreivedData={classAssessments} />
        ) : (
          <ClassDetailsSkeleton />
        )}
      </Grid>
      <Grid
        item
        xs={12}
        md={6}
        sx={{
          mt: {
            xs: 2,
            md: 0,
          },
        }}
      >
        {classAssessments ? (
          <AssessmentByClassHorizontalBar
            retreivedData={assessmentsByClassData(
              classAssessments?.classes,
              classAssessments?.assessments,
              classAssessments?.students
            )}
          />
        ) : (
          <ClassDetailsSkeleton />
        )}
      </Grid>
    </Grid>
  );
};

export default ClassDetailsWrapper;
