import React, { useState, useContext, useEffect } from "react";
import { Formik, Form } from "formik";
import { Card, Grid } from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import Loader from "components/atoms/Loader/Loader";
import FormField from "components/atoms/FormField";
import validations from "./schemas/validations";
import form from "./schemas/form";
import initialValues from "./schemas/initialValues";
import DeleteInstituteModal from "components/molecules/DeleteInstituteModal/DeleteInstituteModal";

function InstituteForm() {
  const { apiService } = useContext(ApiServiceContext);
  const [initialData, setInitialData] = useState(initialValues);
  const [isEditing, setIsEditing] = useState(false);
  const [submitLoader, setSubmitLoader] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteLoader, setDeleteLoader] = useState(false);
  const { formId, formField } = form;
  const { name, program, addressOne, addressTwo, city, state, pincode } =
    formField;

  const fetchInstituteData = async () => {
    if (!apiService || !apiService.instituteId) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const instituteData = await apiService.getInstitute();

      setInitialData({
        name: instituteData.name,
        program: instituteData.program,
        addressOne: instituteData.address.addressOne || "",
        addressTwo: instituteData.address.addressTwo || "",
        city: instituteData.address.city || "",
        state: instituteData.address.state || "",
        pincode: instituteData.address.pincode || "",
      });
    } catch (error) {
      toast.error("Error fetching institute details");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Fetch only institute data
    fetchInstituteData();
  }, [apiService]);

  const handleSubmit = async (values, actions) => {
    setSubmitLoader(true);
    const address = {
      addressOne: values[addressOne.name],
      addressTwo: values[addressTwo.name],
      city: values[city.name],
      pincode: values[pincode.name],
      state: values[state.name],
    };

    const formData = {
      name: values[name.name],
      program: values[program.name],
      address,
    };

    try {
      await apiService.editInstitute(formData);
      toast.success("Institute details updated successfully");
      setIsEditing(false);
    } catch (err) {
      actions.setErrors({ submit: "An error occurred. Please try again." });
    } finally {
      setSubmitLoader(false);
    }
  };

  const handleDeleteInstitute = async () => {
    setDeleteLoader(true);
    try {
      await apiService.deleteInstitute();
      toast.success("Institute deleted successfully");
      window.location.reload(); // Refresh the page

      // Clear the instituteId from apiService and session storage
      apiService.setInstituteId("");
    } catch (err) {
      toast.error(err.message || "Error deleting institute");
    } finally {
      setDeleteLoader(false);
      setIsDeleteModalOpen(false);
    }
  };

  return (
    <>
      <MDBox px={3} py={2}>
        <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems="start"
          mb={2}
        >
          <MDTypography variant="h5" fontWeight="medium">
            Institute Information
          </MDTypography>
          <MDBox>
            <MDButton
              variant="gradient"
              color="info"
              onClick={() => setIsEditing(!isEditing)}
              sx={{ mr: 1 }}
            >
              {isEditing ? "Cancel" : "Edit"}
            </MDButton>
            {!isEditing && (
              <MDButton
                variant="gradient"
                color="error"
                onClick={() => setIsDeleteModalOpen(true)}
              >
                Delete Institute
              </MDButton>
            )}
          </MDBox>
        </MDBox>

        <Formik
          initialValues={initialData}
          validationSchema={validations[0]}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, errors, touched, handleChange }) => (
            <Form id={formId} autoComplete="off">
              {isLoading ? (
                <MDBox
                  width="100%"
                  height="300px"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Loader message={"Loading Institute Data..."} />
                </MDBox>
              ) : (
                <MDBox>
                  <Grid item xs={12} sm={3}>
                    <MDBox>
                      <MDTypography variant="h6" fontWeight="medium">
                        Program
                      </MDTypography>
                      <MDTypography variant="body2">
                        {values[program.name] || "-"}
                      </MDTypography>
                    </MDBox>
                  </Grid>

                  <Grid container spacing={3} sx={{ pt: 2 }}>
                    {[name, addressOne, addressTwo, city, state, pincode].map(
                      ({ type, label, name, placeholder }) => (
                        <Grid item xs={12} sm={6} key={name}>
                          {!isEditing ? (
                            <MDBox>
                              <MDTypography variant="h6" fontWeight="medium">
                                {label.replace(/\s*\*+$/, "")}
                              </MDTypography>
                              <MDTypography variant="body2">
                                {values[name] || "-"}
                              </MDTypography>
                            </MDBox>
                          ) : (
                            <FormField
                              type={type}
                              label={label}
                              name={name}
                              value={values[name]}
                              placeholder={placeholder}
                              onChange={handleChange}
                              error={errors[name] && touched[name]}
                              disabled={!isEditing}
                            />
                          )}
                        </Grid>
                      )
                    )}
                  </Grid>
                </MDBox>
              )}

              {isEditing && !isLoading && (
                <MDBox
                  display="flex"
                  justifyContent="flex-end"
                  alignItems="center"
                >
                  <MDButton
                    type="submit"
                    variant="gradient"
                    color="dark"
                    sx={{ mt: 2, width: "10rem" }}
                    disabled={submitLoader}
                  >
                    {submitLoader ? <Loader /> : "Save Changes"}
                  </MDButton>
                </MDBox>
              )}
            </Form>
          )}
        </Formik>
      </MDBox>

      {/* Delete Warning Modal */}
      <DeleteInstituteModal
        isDeleteModalOpen={isDeleteModalOpen}
        setIsDeleteModalOpen={setIsDeleteModalOpen}
        deleteLoader={deleteLoader}
        handleDeleteInstitute={handleDeleteInstitute}
      />
    </>
  );
}

export default InstituteForm;
