import React, { useEffect, useContext } from "react";
import Grid from "@mui/material/Grid";
import MDBox from "components/atoms/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import { ApiServiceContext } from "context";
import InstituteInfoCard from "./components/InstituteInfoCard";
import OverallStatsCards from "./components/OverallStatsCards";
import ReportsBarChartWrapper from "./components/ReportsBarChartWrapper";
import ReportsLineChartWrapper from "./components/ReportsLineChartWrapper";
import ClassDetailsWrapper from "./components/ClassDetailsWrapper";

function Institute() {
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);

  // Login handling
  useEffect(() => {
    if (!loading && !apiService) {
      handleLogin(window.location.href);
    }
  }, [apiService, loading]);

  return (
    <DashboardLayout>
      <DashboardNavbar isHome={true} title="Institute Dashboard" />
      <MDBox pt={1} pb={3}>
        <InstituteInfoCard />
        <OverallStatsCards />

        <MDBox mt={7}>
          <Grid
            container
            spacing={{
              xs: 7,
              md: 3,
            }}
          >
            <ReportsBarChartWrapper />
            <ReportsLineChartWrapper />
          </Grid>
        </MDBox>

        <Grid container mt={5}>
          <ClassDetailsWrapper />
        </Grid>
      </MDBox>
    </DashboardLayout>
  );
}

export default Institute;
