import React from "react";
import { Formik, Form } from "formik";
import Grid from "@mui/material/Grid";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import FormField from "components/atoms/FormField";
import validations from "components/organisms/InstituteForm/schemas/validations";
import form from "components/organisms/InstituteForm/schemas/form";
import initialValues from "components/organisms/InstituteForm/schemas/initialValues";
import MDButton from "components/atoms/MDButton";

function AddInstituteForm({
  onSubmit,
  initialValues: propInitialValues = {},
}) {
  const { formId, formField } = form;
  const { program, name, addressOne, addressTwo, city, state, pincode } =
    formField;

  // Merge prop initial values with default initial values
  const mergedInitialValues = {
    ...initialValues,
    ...propInitialValues,
  };

  return (
    <>
      <MDBox mb={3}>
        <MDTypography variant="h5" fontWeight="bold" color="dark">
          Add Institute Information
        </MDTypography>
        <MDTypography variant="body2" color="dark">
          Fill in the required details to register your institute.
        </MDTypography>
      </MDBox>

      <Formik
        initialValues={mergedInitialValues}
        validationSchema={validations[0]}
        onSubmit={onSubmit}
      >
        {({ values, errors, touched, handleChange }) => (
          <Form id={formId} autoComplete="off">
            {/* Form Fields */}
            <MDBox mb={1}>
              <Grid container spacing={4}>
                <Grid item xs={12} md={6}>
                  <FormField
                    type={program.type}
                    label={program.label}
                    name={program.name}
                    value={"CBSE"}
                    placeholder={program.placeholder}
                    onChange={handleChange}
                    error={errors[program.name] && touched[program.name]}
                    disabled={true}
                  />
                </Grid>
              </Grid>
            </MDBox>
            <Grid container spacing={4}>
              {[name, addressOne, addressTwo, city, state, pincode].map(
                ({ type, label, name, placeholder }) => (
                  <Grid item xs={12} md={6} key={name}>
                    <FormField
                      type={type}
                      label={label}
                      name={name}
                      value={values[name]}
                      placeholder={placeholder}
                      onChange={handleChange}
                      error={errors[name] && touched[name]}
                    />
                  </Grid>
                )
              )}
            </Grid>

            <MDTypography
              fontWeight="light"
              variant="button"
              className="flex justify-between p-1 pt-2"
            >
              Fields with * are mandatory
            </MDTypography>

            {/* Submit Button */}
            <MDBox
              display="flex"
              alignItems="center"
              justifyContent="end"
              sx={{ mt: 2 }}
            >
              <MDButton
                type="submit"
                variant="gradient"
                color="dark"
                sx={{ width: "10rem" }}
              >
                Next
              </MDButton>
            </MDBox>
          </Form>
        )}
      </Formik>
    </>
  );
}

export default AddInstituteForm;
