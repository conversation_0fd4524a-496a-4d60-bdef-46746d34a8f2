import React from "react";
import MDBox from "../MDBox";
import MDTypography from "../MDTypography";
import { Grid } from "@mui/material";

function InstituteActionCard({
  icon: Icon,
  title,
  onClick,
  gridProps = { xs: 12 },
}) {
  return (
    <Grid item {...gridProps}>
      <MDBox
        onClick={onClick}
        sx={{
          px: 3,
          py: 3,
          cursor: "pointer",
          display: "flex",
          alignItems: "center",
          gap: 2,
          boxShadow: 2,
          borderRadius: 3,
          border: "1px solid #c9c9c9",
          background: "linear-gradient(135deg, #f9f9f9, #ffffff)",
          transition: "all 0.3s ease-in-out",
          "&:hover": {
            boxShadow: 4,
            transform: "scale(1.01)",
          },
        }}
      >
        {Icon && <Icon fontSize="medium" />}

        <MDTypography variant="h6" fontWeight="bold">
          {title}
        </MDTypography>
      </MDBox>
    </Grid>
  );
}

export default InstituteActionCard;

