import React, { useContext, useEffect, useState } from "react";
import Grid from "@mui/material/Grid";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import AssessmentsBySubjectChart from "../AssessmentsBySubjectChart";
import ChartsSkeleton from "../Loaders/ChartsSkeleton";
import MonthlyAssessmentsChartWrapper from "../MonthlyAssessmentsChartWrapper";

const SubjectAssessmentChartsWrapper = ({ grade }) => {
  const { apiService } = useContext(ApiServiceContext);
  const [assessmentData, setAssessmentData] = useState(null);
  const [loading, setLoading] = useState(true);

  const colors = [
    "primary",
    "secondary",
    "info",
    "success",
    "warning",
    "error",
    "light",
    "dark",
  ];

  const processColorMap = (subjects) => {
    const subjectColorMap = {};
    subjects.forEach((subject, index) => {
      subjectColorMap[subject] = colors[index % colors.length];
    });
    return subjectColorMap;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await apiService.getClassAssessmentsBySubject(grade);
        const colorMap = data.subjects ? processColorMap(data.subjects) : null;
        setAssessmentData({ data, colorMap });
      } catch (err) {
        toast.error("Error loading assessment data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [apiService, grade]);

  if (loading) return <ChartsSkeleton />;

  return (
    <Grid container spacing={3}>
      {assessmentData?.data && (
        <Grid item xs={12} md={6}>
          <AssessmentsBySubjectChart
            data={assessmentData.data}
            colorMap={assessmentData.colorMap}
          />
        </Grid>
      )}
      {assessmentData?.colorMap && (
        <MonthlyAssessmentsChartWrapper 
          grade={grade} 
          colorMap={assessmentData.colorMap} 
        />
      )}
    </Grid>
  );
};

export default SubjectAssessmentChartsWrapper;


