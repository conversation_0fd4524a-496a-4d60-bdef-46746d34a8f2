import React, { useContext, useEffect, useState } from "react";
import { Card, Skeleton } from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";

const InstituteInfoCard = () => {
  const { apiService } = useContext(ApiServiceContext);

  const [instituteDetails, setInstituteDetails] = useState(null);

  useEffect(() => {
    if (instituteDetails) return;
    apiService
      .getInstitute()
      .then((data) => setInstituteDetails(data))
      .catch(() => {
        return toast.error("Error loading institute details");
      });
  }, []);

  return (
    <Card
      sx={{
        padding: {
          xs: "0 10px",
          sm: "10px 20px",
        },
      }}
    >
      <MDBox
        sx={{
          display: "flex",
          flexDirection: {
            xs: "column",
            sm: "row",
          },
          alignItems: {
            xs: "start",
            sm: "center",
          },
          justifyContent: "space-between",
          gap: "10px",
          padding: {
            xs: "20px",
            sm: "10px 20px",
          },
          height: "100%",
          width: "100%",
        }}
      >
        <MDTypography
          variant="h5"
          fontWeight="medium"
          fontSize={{
            xs: "1.5rem",
            sm: "1.25rem",
          }}
        >
          {instituteDetails ? (
            instituteDetails.name
          ) : (
            <Skeleton animation="wave" width={200} />
          )}
        </MDTypography>
        <MDTypography
          variant="h5"
          fontWeight="medium"
          fontSize={{
            xs: "1.5rem",
            sm: "1.25rem",
          }}
        >
          {instituteDetails ? (
            instituteDetails.program
          ) : (
            <Skeleton animation="wave" width={150} />
          )}
        </MDTypography>
      </MDBox>
    </Card>
  );
};

export default InstituteInfoCard;
