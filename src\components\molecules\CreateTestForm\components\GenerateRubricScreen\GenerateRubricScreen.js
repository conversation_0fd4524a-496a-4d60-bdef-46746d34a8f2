import React from "react";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import Loader from "components/atoms/Loader/Loader";
import { Icon } from "@mui/material";
import AiFeaturesIcon from "assets/images/ai-features.svg";
import AutoAwesomeIcon from "@mui/icons-material/AutoAwesome";
import FileCopyIcon from "@mui/icons-material/FileCopy";

const GenerateRubricScreen = ({
  rubricLoading,
  handleGenerateRubrics,
  values,
  setUploadModalOpen,
  setIsShowRubricScreen,
  setIsRubricFromFiles,
  isValuesChanged,
  setIsValuesChanged,
  setActiveStep,
  setIsRubricGenerated,
  setIsRubricButtonClicked,
}) => {
  return (
    <MDBox
      sx={{
        height: {
          xs: "calc(100vh - 8rem)",
          md: "calc(100vh - 23rem)",
        },
        display: "flex",
        flexDirection: "column",
        justifyContent: { xs: "start", md: "center" },
        alignItems: "center",
        px: { xs: 2, sm: 4, xl: 0 },
        mt: { xs: 2, md: 4 },
        mb: { xs: 15, md: 0 },
      }}
    >
      <MDBox
        display="flex"
        flexDirection={{ xs: "column", md: "row" }}
        justifyContent="space-between"
        alignItems="center"
        width="100%"
        gap={{
          xs: 1,
          md: 3,
        }}
      >
        {/* Left Section: Generate Rubric using File Upload */}
        <MDBox
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          width={{ xs: "100%", md: "48%" }}
        >
          <FileCopyIcon color="info" fontSize="large" />

          <MDTypography variant="h4" my={2} textAlign="center">
            Generate Rubric from Files
          </MDTypography>

          <MDTypography variant="h6" mb={4} textAlign="center">
            Upload your answer sheets to auto-fill the rubric.
          </MDTypography>

          <MDButton
            variant="outlined"
            color="info"
            onClick={() => {
              setIsRubricFromFiles(true);
              setUploadModalOpen(true);
            }}
            sx={{
              display: "flex",
              alignItems: "center",
            }}
          >
            <Icon sx={{ marginRight: "8px", marginBottom: "2px" }}>image</Icon>
            Auto-fill from files
          </MDButton>
        </MDBox>

        <hr
          style={{
            border: "none",
            borderRight: "2px solid #a9a9a9",
            height: "auto",
            margin: 0,
            alignSelf: "stretch",
            display: { xs: "none", md: "block" },
          }}
        />

        <MDTypography
          variant="h6"
          my={2}
          textAlign="center"
          sx={{
            display: { xs: "block", md: "none" },
          }}
        >
          OR
        </MDTypography>

        {/* Right Section: Generate Rubric using AI */}
        <MDBox
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          width={{ xs: "100%", md: "48%" }}
        >
          <AutoAwesomeIcon color="warning" fontSize="large" />

          <MDTypography variant="h4" my={2} textAlign="center">
            Generate Rubric with AI
          </MDTypography>

          <MDTypography variant="h6" mb={4} textAlign="center">
            Use AI to automatically generate a rubric based on your questions.
          </MDTypography>

          <MDButton
            disabled={rubricLoading}
            variant="outlined"
            color="warning"
            onClick={() => {
              handleGenerateRubrics();
            }}
            sx={{
              display: "flex",
              alignItems: "center",
              width: "12rem",
            }}
          >
            {rubricLoading ? (
              <Loader loaderColor="warning" />
            ) : (
              <>
                <img
                  src={AiFeaturesIcon}
                  alt="AI"
                  height={20}
                  width={20}
                  style={{ marginRight: "10px" }}
                />
                <p>Generate Rubric</p>
              </>
            )}
          </MDButton>
        </MDBox>
      </MDBox>

      {/* Additional Section: OR Button */}
      <MDBox
        display="flex"
        flexDirection="column"
        alignItems="center"
        mt={4}
        width="100%"
      >
        <MDTypography variant="h6" mb={2} textAlign="center">
          OR
        </MDTypography>

        <MDTypography variant="h6" mb={4} textAlign="center">
          You can also manually create a rubric to suit your needs.
        </MDTypography>

        <MDButton
          variant="outlined"
          color="primary"
          onClick={() => {
            setIsShowRubricScreen(false);
            setIsRubricGenerated(true);
            setIsRubricButtonClicked(true);
            setActiveStep(1);
            isValuesChanged && setIsValuesChanged(false);
          }}
          sx={{
            display: "flex",
            alignItems: "center",
          }}
        >
          <Icon sx={{ marginRight: "8px", marginBottom: "3px" }}>edit</Icon>
          Manually Add Rubric
        </MDButton>
      </MDBox>
    </MDBox>
  );
};

export default GenerateRubricScreen;
