import { useState, useEffect, useContext, useCallback } from "react";
import { ApiServiceContext } from "context";
import checkIsCurrentTerm from "utils/helperFunctions/checkIsCurrentTerm";

export const useTermStatus = () => {
  const { apiService } = useContext(ApiServiceContext);
  const [hasCurrentTerm, setHasCurrentTerm] = useState(false);
  const [termsLoading, setTermsLoading] = useState(true);

  const checkTerms = useCallback(async () => {
    if (!apiService?.instituteId) return;

    try {
      setTermsLoading(true);
      const terms = await apiService.getTerms();
      const hasActive = terms.some((term) =>
        checkIsCurrentTerm(term.startDate, term.endDate)
      );
      setHasCurrentTerm(hasActive);
    } catch (error) {
      setHasCurrentTerm(false);
    } finally {
      setTermsLoading(false);
    }
  }, [apiService]);

  useEffect(() => {
    if (apiService?.instituteId) {
      checkTerms();
    }
  }, [apiService?.instituteId, checkTerms]);

  return { hasCurrentTerm, termsLoading, checkTerms };
};

