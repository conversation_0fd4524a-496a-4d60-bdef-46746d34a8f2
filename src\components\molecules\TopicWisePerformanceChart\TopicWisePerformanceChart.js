import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { Card } from "@mui/material";
import Loader from "components/atoms/Loader/Loader";

const getTopicPerformanceChartOptions = (data) => {
  if (!data || data.length === 0) return null;

  const topicsWithScores = [];

  data.forEach((chapter) => {
    chapter.topicsWithScore.forEach((topic) => {
      if (topic.topicScore !== undefined) {
        topicsWithScores.push({
          topic: topic.topic,
          score: topic.topicScore,
        });
      }
    });
  });

  // Sort topics by score in descending order
  topicsWithScores.sort((a, b) => b.score - a.score);

  const sortedTopics = topicsWithScores.map((item) => item.topic);
  const sortedScores = topicsWithScores.map((item) => item.score);

  return {
    colors: ["#344767"],
    chart: {
      type: "column",
      height: "600px",
      spacing: [30, 30, 0, 30],
      borderRadius: 20,
    },
    title: {
      text: "Topic-Wise Performance",
      align: "left",
      margin: 50,
    },
    legend: {
      enabled: false,
    },
    xAxis: {
      categories: sortedTopics,
      title: {
        text: "Topics",
      },
      labels: {
        formatter: function () {
          return this.value.length > 20
            ? this.value.substring(0, 20) + "..."
            : this.value;
        },
        rotation: -60,
        align: "right",
      },
      crosshair: true,
    },
    yAxis: {
      min: 0,
      max: 10,
      title: {
        text: "Score",
      },
    },
    tooltip: {
      headerFormat: '<span style="font-size:12px">{point.key}</span><br/>',
      pointFormat:
        '<span style="color:{series.color}">Score</span>: <b>{point.y:.1f}</b>',
      shared: true,
      useHTML: true,
      outside: true,
    },
    plotOptions: {
      column: {
        dataLabels: {
          enabled: true,
          format: "{point.y:.1f}",
        },
        borderRadius: 3,
      },
    },
    credits: {
      enabled: false,
    },
    series: [
      {
        name: "Score",
        data: sortedScores,
      },
    ],
  };
};

const TopicWisePerformanceChart = ({ data }) => {
  const [chartOptions, setChartOptions] = useState(null);

  useEffect(() => {
    if (data && data.length > 0) {
      setChartOptions(getTopicPerformanceChartOptions(data));
    }
  }, [data]);

  return (
    <>
      {chartOptions ? (
        <Card sx={{ paddingBottom: "30px !important" }}>
          <HighchartsReact highcharts={Highcharts} options={chartOptions} />
        </Card>
      ) : (
        <Loader />
      )}
    </>
  );
};

export default TopicWisePerformanceChart;
