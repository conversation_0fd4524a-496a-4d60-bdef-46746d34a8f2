/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://eddyowl.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://eddyowl.com)

Coded by eddyowl.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// @mui material components
import Link from "@mui/material/Link";

// Material Dashboard 2 PRO React components
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";

// Material Dashboard 2 PRO React base styles
import typography from "assets/theme/base/typography";

const companyInfo = { href: "https://eddyowl.com/", name: "EddyOwl" };
const allLinks = [
  { href: "https://eddyowl.com/", name: "EddyOwl" },
  { href: "https://eddyowl.com/#about-us", name: "About Us" },
  { href: "https://eddyowl.com/#contact-us", name: "Contact Us" },
  { href: "https://eddyowl.com/terms", name: "Terms of Use" },
  { href: "https://eddyowl.com/privacy", name: "Privacy Policy" },
];

function Footer({ company = companyInfo, links = allLinks }) {
  const { href, name } = company;
  const { size } = typography;

  const renderLinks = () =>
    links.map((link) => (
      <MDBox key={link.name} component="li" px={2} lineHeight={1}>
        <Link href={link.href} target="_blank">
          <MDTypography variant="button" fontWeight="regular" color="text">
            {link.name}
          </MDTypography>
        </Link>
      </MDBox>
    ));

  return (
    <MDBox
      width="100%"
      display="flex"
      flexDirection={{ xs: "column", lg: "row" }}
      justifyContent="space-between"
      alignItems="center"
      px={1.5}
    >
      <MDBox
        display="flex"
        justifyContent="center"
        alignItems="center"
        flexWrap="wrap"
        color="text"
        fontSize={size.sm}
        px={1.5}
      >
        &copy;
        <MDBox fontSize={size.md} color="text" mb={-0.5} mx={0.25}></MDBox>
        <Link href={href} target="_blank">
          <MDTypography variant="button" fontWeight="medium">
            &nbsp;{name}&nbsp;
          </MDTypography>
        </Link>
        {new Date().getFullYear()}. All Rights Reserved
      </MDBox>
      <MDBox
        component="ul"
        sx={({ breakpoints }) => ({
          display: "flex",
          flexWrap: "wrap",
          alignItems: "center",
          justifyContent: "center",
          listStyle: "none",
          mt: 2,
          mb: 0,
          p: 0,

          [breakpoints.up("lg")]: {
            mt: 0,
          },
        })}
      >
        {renderLinks()}
      </MDBox>
    </MDBox>
  );
}

// Typechecking props for the Footer
Footer.propTypes = {
  company: PropTypes.objectOf(PropTypes.string),
  links: PropTypes.arrayOf(PropTypes.object),
};

export default Footer;
