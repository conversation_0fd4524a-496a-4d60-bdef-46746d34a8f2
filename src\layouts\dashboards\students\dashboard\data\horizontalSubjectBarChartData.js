const colorMap = {
    "Maths": "info",
    "Science": "primary",
    "English": "dark",
    "Hindi": "secondary",
  };
  
  const horizontalSubjectBarChartData = (data) => {
    const subjectsData = {
      labels: data.labels || [],
      datasets: [
        {
          label: "Subjects",
          backgroundColors: [],
          data: data.datasets.data || [],
        },
      ],
    };
    subjectsData.datasets.backgroundColors = subjectsData.labels.map(
      (label) => colorMap[label] || "defaultColor"
    );
    return subjectsData;
  };
  
  export default horizontalSubjectBarChartData;
  