import React from "react";
import { Card, Grid, Skeleton } from "@mui/material";
import MDBox from "components/atoms/MDBox";

const StatsCardsSkeleton = () => {
  return (
    <Grid container spacing={3}>
      {[1, 2, 3].map((item) => (
        <Grid item xs={12} sm={4} key={item}>
          <Card>
            <MDBox p={2} display="flex" justifyContent="space-between">
              <MDBox>
                <Skeleton animation="wave" width={120} height={20} />
                <Skeleton animation="wave" width={60} height={30} />
              </MDBox>
              <Skeleton
                animation="wave"
                variant="circular"
                width={45}
                height={45}
              />
            </MDBox>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default StatsCardsSkeleton;