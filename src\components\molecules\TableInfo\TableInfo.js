import { useContext, useEffect, useState } from "react";
import Tooltip from "@mui/material/Tooltip";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import { truncateText } from "utils/helperFunctions/truncateText";
import USER_ROLES from "utils/helperFunctions/USER_ROLES";
import { ApiServiceContext } from "context";
import Loader from "components/atoms/Loader/Loader";

export const TableInfo = ({
  tableTitle = "",
  tableDesc = "",
  isCustomTitle = false,
  isDescBold = false,
  buttonText = "",
  handleClick = () => {},
  secButtonText = "",
  handleSecClick = () => {},
  thirdButton = null, // New prop for the publish all button
  showUploadMultiple = false, // Add this prop
  handleUploadMultiple, // Add this prop
}) => {
  const [isUploadButton, setIsUploadButton] = useState(false);
  const [subject, setSubject] = useState("");
  const [customTitle, setCustomTitle] = useState("");

  const { apiService } = useContext(ApiServiceContext);
  const isStudentRole = apiService?.role === USER_ROLES.STUDENT_ROLE;

  useEffect(() => {
    setIsUploadButton(
      buttonText.toLowerCase() === "upload from phone".toLowerCase()
    );
  }, [buttonText]);

  // Extract custom title (before pipe) and subject (after pipe)
  useEffect(() => {
    if (isCustomTitle && tableTitle.includes("|")) {
      const [beforePipe, afterPipe] = tableTitle.split("|");
      setCustomTitle(beforePipe.trim()); // Before pipe
      setSubject(afterPipe.trim()); // After pipe
    }
  }, [isCustomTitle, tableTitle]);

  // Tooltip title for the full table title
  const tooltipTitle = tableTitle
    .replace(/^Submissions for /, "") // Remove "Submissions for"
    .replace(/\s*\|.*/, ""); // Remove everything after the pipe

  // Render logic for table title
  const renderTableTitle = () => {
    if (isCustomTitle && tableTitle.length > 60) {
      return (
        <>
          <Tooltip title={tooltipTitle}>
            <span style={{ cursor: "pointer" }}>
              {truncateText(customTitle, 40)}
            </span>
          </Tooltip>
          {subject && ` | ${subject}`} {/* Append subject after pipe */}
        </>
      );
    } else {
      return tableTitle;
    }
  };

  return (
    <MDBox px={3} py={2.5}>
      <MDBox
        display="flex"
        justifyContent="space-between"
        alignItems={{ xs: "flex-start", lg: "center" }}
        flexDirection={{ xs: "column", md: "row" }}
        gap={1}
      >
        <MDBox display="flex" flexDirection="column" gap={0.5}>
          <MDTypography
            variant="h5"
            fontWeight="medium"
            sx={{
              fontSize: {
                xs: "1rem",
                sm: "unset",
              },
            }}
          >
            {renderTableTitle()}
          </MDTypography>
          <MDTypography
            variant={isDescBold ? "h6" : "button"}
            color={isDescBold ? "secondary" : "text"}
            fontWeight={isDescBold ? "medium" : "regular"}
          >
            {tableDesc}
          </MDTypography>
        </MDBox>
        <MDBox
          sx={{
            display: "flex",
            width: {
              xs: "100%",
              md: "auto",
            },
            flexDirection: { xs: "column", sm: "row", md: "column", lg: "row" },
            justifyContent: "flex-end",
            gap: 1,
            whiteSpace: "nowrap",
          }}
        >
          {thirdButton && thirdButton} {/* Render the publish all button */}
          {showUploadMultiple && (
            <MDButton
              variant="outlined"
              color="primary"
              onClick={handleUploadMultiple}
            >
              Upload Multiple
            </MDButton>
          )}
          {secButtonText && (
            <MDButton variant="outlined" color="dark" onClick={handleSecClick}>
              {secButtonText}
            </MDButton>
          )}
          {buttonText !== "" && (
            <MDButton
              variant="gradient"
              color="info"
              onClick={handleClick}
              sx={{
                display: {
                  xs: isUploadButton ? "none" : "block",
                  sm: "block",
                },
                whiteSpace: "nowrap",
              }}
            >
              {buttonText}
            </MDButton>
          )}
        </MDBox>
      </MDBox>
    </MDBox>
  );
};
