import React, { useContext, useEffect, useState } from "react";
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import DataTable from "examples/Tables/DataTable";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import { ClassTableHeader } from "constants/ClassTableHeader/ClassTableHeader";

const TopPerformingStudentsWrapper = ({ grade }) => {
  const { apiService } = useContext(ApiServiceContext);
  const [classStats, setClassStats] = useState({
    topPerformingStudents: { columns: [], rows: [] },
  });
  const [dataAvailable, setDataAvailable] = useState(true);
  const [tableLoading, setTableLoading] = useState(false);

  useEffect(() => {
    const fetchTopPerformers = async () => {
      try {
        setTableLoading(true);
        const topPerformingStudents =
          await apiService.getClassTopPerformingStudents(grade);

        const formattedData = {
          columns: ClassTableHeader,
          rows: topPerformingStudents.data || [],
        };

        setDataAvailable(
          Array.isArray(topPerformingStudents.data) &&
            topPerformingStudents.data.length > 0
        );

        setClassStats((prev) => ({
          ...prev,
          topPerformingStudents: formattedData,
        }));
      } catch (err) {
        toast.error("Error loading top performers");
      } finally {
        setTableLoading(false);
      }
    };

    fetchTopPerformers();
  }, [apiService, grade]);

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <MDBox pt={3} px={3}>
            <MDTypography variant="h6" fontWeight="medium">
              Top Performing Students
            </MDTypography>
          </MDBox>
          <MDBox py={1}>
            <DataTable
              table={classStats.topPerformingStudents}
              isSorted="desc"
            />
            {!dataAvailable && (
              <MDBox
                width="100%"
                height="100%"
                pt={2}
                pb={8}
                display="flex"
                justifyContent="center"
                alignItems="center"
              >
                <MDTypography variant="h6" fontWeight="medium">
                  Data Not Available !
                </MDTypography>
              </MDBox>
            )}
          </MDBox>
        </Card>
      </Grid>
    </Grid>
  );
};

export default TopPerformingStudentsWrapper;

