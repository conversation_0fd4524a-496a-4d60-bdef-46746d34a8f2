import * as Yup from "yup";
import form from "./form";

const {
  formField: {
    firstName,
    lastName,
    studentId,
    rollNumber,
    class: studentClass,
    section,
    email,
  },
} = form;

const validations = Yup.object().shape({
  [firstName.name]: Yup.string()
    .max(20, "First name must be at most 20 characters")
    .required(firstName.errorMsg),
  [lastName.name]: Yup.string()
    .max(20, "Last name must be at most 20 characters")
    .required(lastName.errorMsg),
  [studentId.name]: Yup.string()
    .max(20, "Student ID must be at most 20 characters")
    .required(studentId.errorMsg),
  [rollNumber.name]: Yup.number()
    .positive("Roll number must be positive")
    .max(9999999999, "Roll number must be at most 10 digits")
    .required(rollNumber.errorMsg),
  [studentClass.name]: Yup.number()
    .min(6, "Class must be at least 6")
    .max(12, "Class must be between 6 and 12")
    .required(studentClass.errorMsg),
  [section.name]: Yup.string().matches(
    /^[A-Z0-9]+$/,
    "Section must contain only letters and numbers"
  ),
  [email.name]: Yup.string()
    .email("Invalid email format")
    .matches(
      /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/,
      "Email must be lowercase"
    ),
});

export default validations;
