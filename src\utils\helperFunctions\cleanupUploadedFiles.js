import AWS from "aws-sdk";

export const cleanupUploadedFiles = async (fileLocations) => {
  // Configure AWS with the required credentials and region
  AWS.config.update({
    accessKeyId: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_ACCESS_KEY,
    secretAccessKey: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_SECRET_KEY,
    region: process.env.REACT_APP_AWS_REGION,
  });

  const s3 = new AWS.S3();

  try {
    for (const fileLocation of fileLocations) {
      await s3
        .deleteObject({
          Bucket: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_BUCKET,
          Key: fileLocation,
        })
        .promise();
      // 
    }
  } catch (err) {
    // 
  }
};

