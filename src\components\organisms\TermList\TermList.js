import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import TermCard from "components/molecules/TermCard/TermCard";
import DISPLAY_DATE_FORMAT from "constants/DISPLAY_DATE_FORMAT/DISPLAY_DATE_FORMAT";
import Grid from "@mui/material/Grid";

function TermList({
  terms,
  handleEditTerm,
  handleAddMoreTerm,
  handleDeleteTerm,
  isAdvancedSettings = false,
  hasCurrentTerm = false,
}) {
  const [displayTermList, setDisplayTermList] = useState([]);

  useEffect(() => {
    setDisplayTermList(
      terms.map((term) => ({
        ...term,
        startDate: dayjs(term.startDate).format(DISPLAY_DATE_FORMAT),
        endDate: dayjs(term.endDate).format(DISPLAY_DATE_FORMAT),
      }))
    );
  }, [terms]);

  return (
    <MDBox
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      sx={{
        position: "relative",
        minHeight: isAdvancedSettings
          ? { xs: "50vh", sm: "calc(100vh - 21rem)" }
          : "unset",
      }}
    >
      <MDBox>
        {/* Header Section */}
        <MDBox>
          <MDBox
            display="flex"
            flexDirection={
              isAdvancedSettings ? "row" : { xs: "column", sm: "row" }
            }
            justifyContent={isAdvancedSettings ? "flex-end" : "space-between"}
            alignItems={
              isAdvancedSettings ? "center" : { xs: "flex-start", sm: "center" }
            }
            mb={2}
            sx={{
              gap: 2,
              width: "100%",
            }}
          >
            {!isAdvancedSettings && (
              <MDBox>
                <MDTypography variant="h5" fontWeight="bold" color="dark">
                  Added Terms
                </MDTypography>
                <MDTypography variant="body2" color="dark">
                  Click on a term to edit
                </MDTypography>
              </MDBox>
            )}
            <MDButton
              variant="outlined"
              color="info"
              sx={{
                width: "10rem",
                mt: isAdvancedSettings && {
                  xs: 0,
                  md: hasCurrentTerm ? -15 : -38,
                },
                marginLeft: !isAdvancedSettings && "auto",
              }}
              onClick={handleAddMoreTerm}
            >
              Add More Term
            </MDButton>
          </MDBox>
        </MDBox>

        {/* Terms List Section */}
        <MDBox
          flex={1}
          display="flex"
          flexDirection="column"
          sx={{
            minHeight: 0,
            maxHeight: isAdvancedSettings
              ? "calc(100vh - 25rem)"
              : "calc(100vh - 35rem)",
            overflow: "hidden",
          }}
        >
          <MDBox
            flex={1}
            sx={{
              overflowY: "auto",
              overflowX: "hidden",
              scrollbarWidth: "thin",
              scrollbarColor: "#444 #f1f1f1",
              p: 1,
            }}
          >
            <Grid container spacing={2}>
              {displayTermList?.map((term, index) => (
                <Grid item xs={12} md={isAdvancedSettings ? 6 : 12} key={index}>
                  <MDBox
                    onClick={() => !isAdvancedSettings && handleEditTerm(index)}
                    style={{
                      cursor: isAdvancedSettings ? "default" : "pointer",
                    }}
                  >
                    <TermCard
                      name={term.name}
                      startDate={term.startDate}
                      endDate={term.endDate}
                      onDelete={() => handleDeleteTerm(index)}
                    />
                  </MDBox>
                </Grid>
              ))}
            </Grid>
          </MDBox>
        </MDBox>
      </MDBox>

      {/* Footer Notes Section */}
      {terms.length > 0 && (
        <MDBox mt={2} pt={2} borderTop="1px solid #eee">
          <MDTypography variant="h6" color="dark">
            *Note:
          </MDTypography>
          <ul style={{ listStyleType: "disc", paddingLeft: "20px" }}>
            <li>
              <MDTypography variant="body2" color="dark">
                Card highlighted with green border indicates
                <strong style={{ color: "#5b5b5b" }}> Current Term.</strong>
              </MDTypography>
            </li>
            <li>
              <MDTypography variant="body2" color="dark">
                A current term is required to access all features.
              </MDTypography>
            </li>
          </ul>
        </MDBox>
      )}
    </MDBox>
  );
}

export default TermList;
