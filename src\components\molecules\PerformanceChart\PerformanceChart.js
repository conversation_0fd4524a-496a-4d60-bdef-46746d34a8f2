import React from "react";
import TopicWisePerformanceChart from "../TopicWisePerformanceChart/TopicWisePerformanceChart";
import SubmissionPerformanceSpiderChart from "../SubmissionPerformanceSpiderChart/SubmissionPerformanceSpiderChart";
import { Grid } from "@mui/material";

const PerformanceChart = ({ data }) => {
  return (
    <Grid container spacing={2} mt={0}>
      <Grid item lg={6} xs={12}>
        <TopicWisePerformanceChart data={data} />
      </Grid>
      <Grid item lg={6} xs={12}>
        <SubmissionPerformanceSpiderChart data={data} />
      </Grid>
    </Grid>
  );
};

export default PerformanceChart;
