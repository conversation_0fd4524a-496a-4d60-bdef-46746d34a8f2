import USER_ROLES from "utils/helperFunctions/USER_ROLES";

class ApiService {
  constructor(
    token,
    baseUrl,
    handleError,
    redirectToSwitchInstitute,
    userEmail
  ) {
    this.token = token;
    this.baseUrl = baseUrl;
    this.instituteId = null; // Initialize instituteId
    this.role = null; // Initialize role
    this.studentId = null; // Initialize studentId
    this.userEmail = userEmail; // Add user email from Auth0
    this.handleError = handleError;
    this.redirectToSwitchInstitute = redirectToSwitchInstitute;
  }

  async fetchData(
    url,
    method,
    body = null,
    additionalHeaders = {},
    retries = 3,
    signal = null // Add the signal parameter for cancellation
  ) {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        this.validateApiConfig(); // Validate API configuration

        const headers = {
          Authorization: `Bearer ${this.token}`,
          ...additionalHeaders,
        };

        const options = {
          method,
          headers,
          signal, // Pass the signal here for cancellation
        };

        if (body) {
          options.body = body instanceof FormData ? body : JSON.stringify(body);
          if (!(body instanceof FormData)) {
            headers["Content-Type"] = "application/json";
          }
        }

        const response = await fetch(`${this.baseUrl}/${url}`, options);
        if (!response.ok) {
          const errorText = await response.text();

          const cleanedErrorText = errorText.replace(/^"|"$/g, "").trim(); // Remove surrounding quotes and trim any extra spaces/newlines
          const finalErrorText = cleanedErrorText.replace(/"$/, "");

          const isAutoRubricError =
            response.status === 500 &&
            (finalErrorText === "generating rubric failed" ||
              finalErrorText === "length of rubric inconsistent with input");

          if (isAutoRubricError) {
            throw new Error(`Error: ${response.status} - ${errorText}`);
          }

          if (attempt === retries || response.status < 500) {
            this.handleError(response.status, errorText);
            break;
          }

          await new Promise((resolve) => setTimeout(resolve, 1000));

          throw new Error(
            `Network response was not ok: ${response.status} - ${response.statusText}`
          );
        }
        return response.json();
      } catch (error) {
        if (attempt === retries) {
          throw error;
        }
        await new Promise((resolve) => setTimeout(resolve, 1000 * attempt)); // Exponential backoff
      }
    }
  }

  validateApiConfig() {
    if (!this.baseUrl || !this.token) {
      const errorMessage = "API base URL or token is missing.";

      throw new Error(errorMessage);
    }
  }

  setUserRole(role) {
    this.role = role;
  }

  setInstituteId(instituteId) {
    this.instituteId = instituteId;
    sessionStorage.setItem("SELECTED_INSTITUTE_ID", instituteId);
  }

  async initInstituteId() {
    try {
      const { roles } = await this.getUserRoles();

      const storedInstituteId = sessionStorage.getItem("SELECTED_INSTITUTE_ID");

      if (storedInstituteId) {
        this.instituteId = storedInstituteId;
        const matchingRole = roles.find(
          (role) => role.instituteId === storedInstituteId
        );
        if (matchingRole) {
          this.role = matchingRole.role;
          if (matchingRole.role === USER_ROLES.STUDENT_ROLE) {
            const studentData = await this.getStudentByEmail(this.userEmail);
            this.studentId = studentData.studentId;
          }
          return true;
        }
      }

      if (roles.length > 0) {
        const studentRole = roles.find(
          (role) => role.role === USER_ROLES.STUDENT_ROLE
        );
        if (studentRole) {
          this.instituteId = studentRole.instituteId;
          this.role = studentRole.role;
          const studentData = await this.getStudentByEmail(this.userEmail);
          this.studentId = studentData.studentId;
        } else {
          this.instituteId = roles[0].instituteId;
          this.role = roles[0].role;
        }
        this.redirectToSwitchInstitute(roles);
        return true;
      }

      if (roles.length === 0) {
        this.instituteId = "";
        return true;
      }
    } catch (error) {
      // TODO: On 404, redirect to create institute page; otherwise, throw to login
    }
  }

  // User Operations
  getUserRoles() {
    return this.fetchData("userRoles", "GET");
  }

  // Institute Operations
  getInstitute(instituteId) {
    return this.fetchData(
      `institute/${instituteId || this.instituteId}`,
      "GET"
    );
  }

  getStudentByEmail(email) {
    return this.fetchData(
      `institute/${this.instituteId}/student/email/${email}`,
      "GET"
    );
  }

  addInstitute(instituteData) {
    return this.fetchData("institute", "POST", instituteData);
  }

  editInstitute(updatedData) {
    return this.fetchData(`institute/${this.instituteId}`, "PUT", updatedData);
  }

  deleteInstitute() {
    return this.fetchData(`institute/${this.instituteId}`, "DELETE");
  }

  // Assignment Operations
  getAllAssignments(params = {}) {
    const queryParams = new URLSearchParams();
    if (params.termId) queryParams.append("termId", params.termId);
    if (params.grade) queryParams.append("grade", params.grade);
    if (params.section) queryParams.append("section", params.section.join(","));
    if (params.subject) queryParams.append("subject", params.subject);

    const url = `institute/${this.instituteId}/assignment${
      queryParams.toString() ? "?" + queryParams.toString() : ""
    }`;
    return this.fetchData(url, "GET");
  }

  createNewAssignments(formData) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment`,
      "POST",
      formData
    );
  }

  getAssignment(assignmentId) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}`,
      "GET"
    );
  }

  editAssignment(assignmentId, updatedData) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}`,
      "PUT",
      updatedData
    );
  }

  deleteAssignment(assignmentId) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}`,
      "DELETE"
    );
  }

  getTerms() {
    return this.fetchData(`institute/${this.instituteId}/terms`, "GET");
  }

  createNewTerm(instituteId, formData) {
    return this.fetchData(`institute/${instituteId}/terms`, "POST", formData);
  }

  deleteTerm(termId) {
    return this.fetchData(
      `institute/${this.instituteId}/term/${termId}`,
      "DELETE"
    );
  }

  generateTopics(assignmentId) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}/topics`,
      "POST"
    );
  }

  generateAssignmentFromFiles(files, signal) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/auto`,
      "POST",
      {
        files: files,
      },
      {},
      3,
      signal
    );
  }

  getStudentAsPerExam(assignmentId) {
    return this.fetchData(
      `institute/${this.instituteId}/submissions?assignmentId=${assignmentId}`,
      "GET"
    );
  }

  getAllSubmissions(assignmentId, studentId) {
    const queryParams = new URLSearchParams();
    if (assignmentId) queryParams.append("assignmentId", assignmentId);
    if (studentId) queryParams.append("studentId", studentId);

    const url = `institute/${this.instituteId}/submissions${
      queryParams.toString() ? "?" + queryParams.toString() : ""
    }`;
    return this.fetchData(url, "GET");
  }

  getSubmissionData(studentId, assignmentId) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}/student/${studentId}/submission`,
      "GET"
    );
  }

  editSubmissions(assignmentId, studentId, submisssionData) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}/student/${studentId}/edit/submission`,
      "PUT",
      submisssionData
    );
  }

  uploadStudentSubmission(assignmentId, studentId, files) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}/student/${studentId}/submission`,
      "PUT",
      { files: files }
    );
  }

  uploadBulkSubmissions(assignmentId, data) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}/submission/bulk`,
      "PUT",
      data
    );
  }

  deleteStudentSubmission(assignmentId, studentId) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}/student/${studentId}/submission`,
      "DELETE"
    );
  }

  getInstructorsDetails() {
    return this.fetchData(`institute/${this.instituteId}/instructor`, "GET");
  }

  deleteInstructor(email) {
    // Change parameter from instructorId to email
    return this.fetchData(
      `institute/${this.instituteId}/instructor?email=${email}`, // Add email as query parameter
      "DELETE"
    );
  }

  editInstructor(instructorData) {
    return this.fetchData(
      `institute/${this.instituteId}/instructor`,
      "PUT",
      instructorData
    );
  }

  createNewInstructor(instructorData) {
    return this.fetchData(
      `institute/${this.instituteId}/instructor`,
      "POST",
      instructorData
    );
  }

  // Student Operations
  getAllStudents(params = {}) {
    const queryParams = new URLSearchParams();
    if (params.termId) queryParams.append("termId", params.termId);
    if (params.class) queryParams.append("class", params.class);
    if (params.sections)
      queryParams.append("sections", params.sections.join(","));

    const url = `institute/${this.instituteId}/student${
      queryParams.toString() ? "?" + queryParams.toString() : ""
    }`;
    return this.fetchData(url, "GET");
  }

  getStudent(studentId, termId = null) {
    const queryParams = new URLSearchParams();
    if (termId) queryParams.append("termId", termId);

    const url = `institute/${this.instituteId}/student/${studentId}${
      queryParams.toString() ? "?" + queryParams.toString() : ""
    }`;
    return this.fetchData(url, "GET");
  }

  deleteStudent(studentId) {
    return this.fetchData(
      `institute/${this.instituteId}/student/${studentId}`,
      "DELETE"
    );
  }

  editStudent(studentData) {
    return this.fetchData(
      `institute/${this.instituteId}/student`,
      "PUT",
      studentData
    );
  }

  createNewStudent(studentData, termId = null) {
    const queryParams = new URLSearchParams();
    if (termId) queryParams.append("termId", termId);

    const url = `institute/${this.instituteId}/student${
      queryParams.toString() ? "?" + queryParams.toString() : ""
    }`;
    return this.fetchData(url, "POST", studentData);
  }

  // Rubric Operations
  generateRubricFromAi(inputData) {
    return this.fetchData(
      `institute/${this.instituteId}/auto/rubric`,
      "PUT",
      inputData
    );
  }

  generateRubricFromFiles(inputData, signal) {
    return this.fetchData(
      `institute/${this.instituteId}/upload/rubric`,
      "PUT",
      inputData,
      {},
      3,
      signal
    );
  }

  // Stats Operations
  getInstituteOverallStats() {
    return this.fetchData(`institute/${this.instituteId}/allstats`, "GET");
  }
  getWeeklyAssessmentsForInstitute() {
    return this.fetchData(
      `institute/${this.instituteId}/assessments/weekly`,
      "GET"
    );
  }
  getMonthlyAssessmentsForInstitute() {
    return this.fetchData(
      `institute/${this.instituteId}/assessments/monthly`,
      "GET"
    );
  }
  getAssessmentsByClass() {
    return this.fetchData(`institute/${this.instituteId}/classdetails`, "GET");
  }
  getClassOverallStats(classno) {
    return this.fetchData(
      `institute/${this.instituteId}/class/${classno}/allstats`,
      "GET"
    );
  }
  getClassAssessmentsBySubject(classno) {
    return this.fetchData(
      `institute/${this.instituteId}/class/${classno}/assessmentsbysub`,
      "GET"
    );
  }
  getClassMonthlyAssessmentsBySubject(classno) {
    return this.fetchData(
      `institute/${this.instituteId}/class/${classno}/assessmentsbysub/monthly`,
      "GET"
    );
  }
  getClassAssessmentsBySections(classno) {
    return this.fetchData(
      `institute/${this.instituteId}/class/${classno}/assessmentsbysections`,
      "GET"
    );
  }
  getClassStudentsBySections(classno) {
    return this.fetchData(
      `institute/${this.instituteId}/class/${classno}/studentsbysections`,
      "GET"
    );
  }
  getClassTopPerformingStudents(classno) {
    return this.fetchData(
      `institute/${this.instituteId}/class/${classno}/topstudents`,
      "GET"
    );
  }

  getStudentPerformance(assignmentId, studentId) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}/student/${studentId}/submission/performance`,
      "GET"
    );
  }

  getStudentOverallStats(studentId) {
    return this.fetchData(
      `institute/${this.instituteId}/student/${studentId}/studentOverallStats`,
      "GET"
    );
  }

  getSubjectsByGrade(gradeNumber) {
    return this.fetchData(
      `institute/${this.instituteId}/subject/grade/${gradeNumber}`,
      "GET"
    );
  }

  // getStudentMonthlyAssignment(studentId) {
  //   return this.fetchData(
  //     `institute/${this.instituteId}/student/${studentId}/submissonsBySubject/monthly`,
  //     "GET"
  //   );
  // }

  generateRubricFromAi(inputData) {
    return this.fetchData(
      `institute/${this.instituteId}/auto/rubric`,
      "PUT",
      inputData
    );
  }

  generateRubricFromFiles(inputData, signal) {
    return this.fetchData(
      `institute/${this.instituteId}/upload/rubric`,
      "PUT",
      inputData,
      {},
      3,
      signal
    );
  }

  publishStudentSubmission(assignmentId, studentId) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}/student/${studentId}/submission/publish`,
      "PUT"
    );
  }

  publishAllSubmissions(assignmentId) {
    return this.fetchData(
      `institute/${this.instituteId}/assignment/${assignmentId}/publish`,
      "PUT"
    );
  }
}

export default ApiService;
