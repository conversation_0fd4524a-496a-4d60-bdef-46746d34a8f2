import React from "react";
import { Grid, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { useLocation } from "react-router-dom";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import checkIsCurrentTerm from "utils/helperFunctions/checkIsCurrentTerm";

function TermCard({ name, startDate, endDate, onDelete }) {
  const location = useLocation();
  const isAdvancedSettings = location.pathname === "/settings/advanced";

  const isCurrentTerm = checkIsCurrentTerm(startDate, endDate);

  return (
    <Grid item xs={12}>
      <MDBox
        sx={{
          px: { xs: 2, sm: 3 },
          py: 2,
          display: "flex",
          flexDirection: "column",
          alignItems: "start",
          gap: { xs: 0, sm: 0.5 },
          borderRadius: 3,
          boxShadow: 2,
          border: isCurrentTerm ? "2px solid #4CAF50" : "1px solid #c9c9c9",
          background: "linear-gradient(135deg, #f9f9f9, #ffffff)",
          transition: "all 0.3s ease-in-out",
          position: "relative",
          maxWidth: isAdvancedSettings ? "unset" : "100%",
          "&:hover": {
            boxShadow: !isAdvancedSettings && 4,
            transform: isAdvancedSettings ? "none" : "scale(1.01)",
          },
          cursor: isAdvancedSettings ? "default" : "pointer",
        }}
      >
        <Tooltip title="Delete Term" placement="top">
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            sx={{
              position: "absolute",
              right: { xs: 5, sm: 15 },
              top: "50%",
              transform: "translateY(-50%)",
              color: "error.main",
            }}
          >
            <DeleteIcon />
          </IconButton>
        </Tooltip>
        <MDBox
          display="flex"
          alignItems="center"
          gap={2}
          width="100%"
          justifyContent="flex-start"
        >
          <MDTypography
            variant="h6"
            color="text"
            display={{ xs: "none", sm: "block" }}
          >
            Name :
          </MDTypography>
          <MDTypography variant="h6" color="dark">
            {name}
          </MDTypography>
        </MDBox>
        <MDBox
          display="flex"
          alignItems="center"
          gap={2}
          width="100%"
          justifyContent="flex-start"
        >
          <MDTypography
            variant="h6"
            color="text"
            display={{ xs: "none", sm: "block" }}
          >
            Period :
          </MDTypography>
          <MDTypography variant="h6" color="dark">
            {`${startDate} To ${endDate}`}
          </MDTypography>
        </MDBox>
      </MDBox>
    </Grid>
  );
}

export default TermCard;
