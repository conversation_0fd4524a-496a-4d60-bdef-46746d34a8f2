import { useAuth0 } from "@auth0/auth0-react";
import { useContext, useEffect } from "react";
import { ApiServiceContext } from "context";

export const Logout = () => {
  const { logout } = useAuth0();
  const { handleLogout, loading } = useContext(ApiServiceContext);
  useEffect(() => {
    if (!loading) {
      handleLogout();
      logout({
        logoutParams: { returnTo: process.env.REACT_APP_HOME_REDIRECT },
      });
    }
  }, [loading]);

  if (loading) {
    return <div>Loading...</div>;
  }
  return null;
};