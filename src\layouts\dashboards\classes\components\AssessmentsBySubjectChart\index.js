/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// @mui material components
import Card from "@mui/material/Card";
import Tooltip from "@mui/material/Tooltip";
import Icon from "@mui/material/Icon";
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import MDButton from "components/atoms/MDButton";
import MDBadgeDot from "components/atoms/MDBadgeDot";
import PieChart from "examples/Charts/PieChart";
import assessmentsBySubjectData from "../../data/assessmentsBySubjectData";

function AssessmentsBySubjectChart({ data, colorMap }) {
  return (
    <Card>
      <MDBox
        sx={{
          height: "26.25rem !important",
          minHeight: "26.25rem !important",
          maxHeight: "26.25rem !important",
        }}
      >
        <MDBox
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          pt={2}
          px={2}
        >
          <MDTypography variant="h6">Assignments By Subject</MDTypography>
          <Tooltip
            title="See number of assignments for every subject"
            placement="bottom"
            arrow
          >
            <MDButton
              variant="outlined"
              color="secondary"
              size="small"
              circular
              iconOnly
            >
              <Icon>priority_high</Icon>
            </MDButton>
          </Tooltip>
        </MDBox>
        <MDBox mt={3}>
          <Grid container alignItems="center">
            <Grid item xs={7}>
              <PieChart chart={assessmentsBySubjectData(data, colorMap)} />
            </Grid>
            <Grid item xs={5}>
              <MDBox
                pr={1}
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "start",
                  overflowY: "auto",
                  scrollbarWidth: "thin",
                  scrollbarColor: "#444 #f1f1f1",
                  maxHeight: "16rem",
                }}
              >
                {assessmentsBySubjectData(data, colorMap).labels.map(
                  (label, index) => (
                    <MDBox key={index}>
                      <MDBadgeDot
                        color={colorMap[label] || "defaultColor"}
                        size="sm"
                        badgeContent={label}
                      />
                    </MDBox>
                  )
                )}
              </MDBox>
            </Grid>
          </Grid>
        </MDBox>
        <MDBox
          pt={2}
          pb={1}
          px={2}
          display="flex"
          flexDirection={{ xs: "column", sm: "row" }}
          mt="auto"
        >
          <MDBox width={{ xs: "100%" }} lineHeight={1}>
            <MDTypography variant="button" color="text" fontWeight="light">
              <strong>Note:</strong>&nbsp;This includes both graded & not graded
              assignments
            </MDTypography>
          </MDBox>
        </MDBox>
      </MDBox>
    </Card>
  );
}

export default AssessmentsBySubjectChart;
