import PropTypes from "prop-types";
import MDBox from "components/atoms/MDBox";
import { useMaterialUIController } from "context";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import ImportExportIcon from "@mui/icons-material/ImportExport";

function DataTableHeadCell({
  width = "auto",
  children,
  sorted = "none",
  align = "left",
  isVisible = true,
  isShowPercentage,
  ...rest
}) {
  const [controller] = useMaterialUIController();
  const { darkMode } = controller;

  return (
    isVisible && (
      <MDBox
        component="th"
        py={1.5}
        px={3}
        sx={({ palette: { light }, borders: { borderWidth } }) => ({
          borderBottom: `${borderWidth[1]} solid ${light.main}`,
          whiteSpace: "nowrap",
        })}
      >
        <MDBox
          {...rest}
          position="relative"
          textAlign={align}
          color={darkMode ? "white" : "secondary"}
          opacity={0.7}
          sx={({ typography: { size, fontWeightBold } }) => ({
            fontSize: size.xs,
            fontWeight: fontWeightBold,
            textTransform: "uppercase",
            cursor: sorted && "pointer",
            userSelect: sorted && "none",
            display: "flex",
            alignItems: "center",
            justifyContent:
              align === "center"
                ? "center"
                : align === "right"
                ? "flex-end"
                : "flex-start",
            gap: "4px",
          })}
        >
          <span>
            {isShowPercentage ? (
              <>
                {children}
                <span
                  style={{
                    marginLeft: "6px",
                    fontSize: "14px",
                  }}
                >
                  %
                </span>
              </>
            ) : (
              children
            )}
          </span>
          {sorted !== "none" && (
            <span>
              {sorted === "desc" ? (
                <KeyboardArrowDownIcon fontSize="medium" />
              ) : sorted === "asce" ? (
                <KeyboardArrowUpIcon fontSize="medium" />
              ) : (
                <ImportExportIcon
                  fontSize="medium"
                  style={{
                    visibility: "hidden",
                  }}
                />
              )}
            </span>
          )}
        </MDBox>
      </MDBox>
    )
  );
}

DataTableHeadCell.propTypes = {
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  children: PropTypes.node.isRequired,
  sorted: PropTypes.oneOf([false, "none", "asce", "desc"]),
  align: PropTypes.oneOf(["left", "right", "center"]),
  isVisible: PropTypes.bool,
  isShowPercentage: PropTypes.bool,
};

export default DataTableHeadCell;
