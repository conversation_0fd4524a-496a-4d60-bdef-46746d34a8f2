import React from "react";
import { Formik, Form } from "formik";
import Grid from "@mui/material/Grid";
import MDBox from "components/atoms/MDBox";
import FormField from "components/atoms/FormField";
import validations from "./schemas/validations";
import form from "./schemas/form";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import initialValues from "./schemas/initialValues";
import Loader from "components/atoms/Loader/Loader";

export function InstructorForm({
  formData,
  handleSubmit,
  loader,
  viewOnly = false,
}) {
  const { formId, formField } = form;

  const { firstName, lastName, email } = formField;

  const fields = [
    { ...firstName, gridProps: { xs: 12, sm: 6 } },
    { ...lastName, gridProps: { xs: 12, sm: 6 } },
    { ...email, gridProps: { xs: 12 } },
  ];

  return (
    <Grid container sx={{ height: "100%", mt: 1 }}>
      <Grid item xs={12}>
        <Formik
          initialValues={formData || initialValues}
          validationSchema={validations}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, errors, touched }) => (
            <Form id={formId} autoComplete="off">
              <Grid container spacing={2}>
                {fields.map((field) => (
                  <Grid item key={field.name} {...field.gridProps}>
                    <FormField
                      type={field.type}
                      label={field.label}
                      name={field.name}
                      value={values[field.name]}
                      placeholder={field.placeholder}
                      error={errors[field.name] && touched[field.name]}
                      success={
                        values[field.name]?.length > 0 && !errors[field.name]
                      }
                      disabled={viewOnly}
                      required
                    />
                  </Grid>
                ))}
              </Grid>

              <MDTypography
                fontWeight="light"
                variant="button"
                className="flex justify-between"
              >
                Fields with * are mandatory
              </MDTypography>

              <MDBox
                display="flex"
                justifyContent="flex-end"
                alignItems="center"
              >
                <MDButton
                  type="submit"
                  variant="gradient"
                  color="dark"
                  sx={{ marginTop: "20px", width: "8rem" }}
                  disabled={loader}
                >
                  {loader ? <Loader loaderColor="#fff" /> : "Submit"}
                </MDButton>
              </MDBox>
            </Form>
          )}
        </Formik>
      </Grid>
    </Grid>
  );
}
