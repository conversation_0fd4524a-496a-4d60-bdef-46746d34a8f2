const assessmentsBySubjectData = (data, colorMap) => {
  const assessmentData = {
    labels: data.subjects || [],
    datasets: {
      label: "Assignments",
      backgroundColors: [],
      data: data.assessments || [],
    },
  };
  assessmentData.datasets.backgroundColors = assessmentData.labels.map(
    (label) => colorMap[label] || "defaultColor"
  );
  return assessmentData;
};

export default assessmentsBySubjectData;


