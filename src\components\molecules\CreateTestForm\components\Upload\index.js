import React, { useState, useEffect, useContext } from "react";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import MDButton from "components/atoms/MDButton";
import { ApiServiceContext } from "context";
import { InputLabel } from "@mui/material";

export function Upload() {
  const [files, setFiles] = useState([]);
  const handleFileChange = (event) => {
    setFiles([...files, ...event.target.files]);
  };

  const handleRemoveFile = (index) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  return (
    <MDBox
      mt={3}
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
    >
      <MDTypography variant="h4" mb={5}>
        Click below to auto-fill questions from files{" "}
      </MDTypography>

      <MDButton
        variant="outlined"
        color="info"
        sx={{ width: "200px", padding: "0", marginBottom: "10px" }}
      >
        <InputLabel
          sx={{
            width: "200px",
            padding: "10px",
            justifyContent: "center",
          }}
          color="primary"
        >
          <input
            key={files.length - 1}
            type="file"
            multiple
            accept="image/*"
            onChange={handleFileChange}
            className="mb-4 hidden"
          />
          <MDTypography>Choose Files</MDTypography>
        </InputLabel>
      </MDButton>
      {files.length > 0 && (
        <MDBox display="flex" flexDirection="column" alignItems="center">
          <MDTypography variant="h6">Selected Files:</MDTypography>
          <ul>
            {files.map((file, index) => (
              <li key={index}>
                {file.name}{" "}
                <MDButton onClick={() => handleRemoveFile(index)}>
                  Remove
                </MDButton>
              </li>
            ))}
          </ul>
        </MDBox>
      )}
    </MDBox>
  );
}
