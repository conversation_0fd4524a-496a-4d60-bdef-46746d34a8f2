import React from "react";
import { Card, Grid } from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import InstituteActionCard from "components/atoms/InstituteActionCard/InstituteActionCard";
import SchoolIcon from "@mui/icons-material/School";
import PersonIcon from "@mui/icons-material/Person";
import USER_ROLES from "utils/helperFunctions/USER_ROLES";

function SelectRoleCard({
  handleContinueAsStudent,
  handleContinueAsInstructor,
  userRoles = [],
}) {
  const containsInstructorRole = !(
    userRoles.length === 1 && userRoles[0]?.role === USER_ROLES.STUDENT_ROLE
  );

  return (
    <Card
      sx={{
        p: 3,
        mb: 4,
        minHeight: containsInstructorRole ? "300px" : "200px",
        width: { xs: "95%", lg: "40%" },
        maxWidth: "500px",
        boxShadow: 3,
        borderRadius: "12px",
        my: "auto",
        mx: "auto",
      }}
    >
      <MDBox>
        <MDTypography variant="h5" mb={-1}>
          Switch Role
        </MDTypography>
        <MDTypography variant="button" color="dark">
          Select how you want to continue
        </MDTypography>
      </MDBox>
      <MDBox
        sx={{
          px: 1,
          pb: 1,
          mt: 2,
        }}
      >
        <Grid container spacing={2}>
          <InstituteActionCard
            icon={SchoolIcon}
            title="Continue as Student"
            onClick={handleContinueAsStudent}
          />
          {containsInstructorRole && (
            <InstituteActionCard
              icon={PersonIcon}
              title="Continue as Instructor"
              onClick={handleContinueAsInstructor}
            />
          )}
        </Grid>
      </MDBox>
    </Card>
  );
}

export default SelectRoleCard;
