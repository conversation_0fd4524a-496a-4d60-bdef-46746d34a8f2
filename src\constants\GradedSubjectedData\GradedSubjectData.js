export const SubjectGradedData = [
    {
        name: "2021",
        y: 1200,
        drilldown: "2021"
    },
    {
        name: "2022",
        y: 1500,
        drilldown: "2022"
    }
];

export const SubjectGradeDataDrillDown = [
    {
        name: "Subjects in 2021",
        id: "2021",
        data: [
            {
                name: "Hindi",
                y: 400,
                drilldown: "2021-Hindi"
            },
            {
                name: "English",
                y: 500,
                drilldown: "2021-English"
            },
            {
                name: "Maths",
                y: 300,
                drilldown: "2021-Maths"
            }
        ]
    },
    {
        name: "Subjects in 2022",
        id: "2022",
        data: [
            {
                name: "Hindi",
                y: 600,
                drilldown: "2022-Hindi"
            },
            {
                name: "English",
                y: 700,
                drilldown: "2022-English"
            },
            {
                name: "Maths",
                y: 200,
                drilldown: "2022-Maths"
            }
        ]
    },
    {
        name: "Hindi 2021",
        id: "2021-Hindi",
        data: [
            {
                name: "January",
                y: 35,
                drilldown: "2021-Hindi-January"
            },
            {
                name: "February",
                y: 45,
                drilldown: "2021-Hindi-February"
            }
        ]
    },
    {
        name: "English 2021",
        id: "2021-English",
        data: [
            {
                name: "January",
                y: 50,
                drilldown: "2021-English-January"
            },
            {
                name: "February",
                y: 40,
                drilldown: "2021-English-February"
            }
        ]
    },
    {
        name: "Maths 2021",
        id: "2021-Maths",
        data: [
            {
                name: "January",
                y: 20,
                drilldown: "2021-Maths-January"
            },
            {
                name: "February",
                y: 35,
                drilldown: "2021-Maths-February"
            }
        ]
    },
    {
        name: "Hindi 2022",
        id: "2022-Hindi",
        data: [
            {
                name: "January",
                y: 65,
                drilldown: "2022-Hindi-January"
            },
            {
                name: "February",
                y: 75,
                drilldown: "2022-Hindi-February"
            }
        ]
    },
    {
        name: "English 2022",
        id: "2022-English",
        data: [
            {
                name: "January",
                y: 85,
                drilldown: "2022-English-January"
            },
            {
                name: "February",
                y: 90,
                drilldown: "2022-English-February"
            }
        ]
    },
    {
        name: "Maths 2022",
        id: "2022-Maths",
        data: [
            {
                name: "January",
                y: 45,
                drilldown: "2022-Maths-January"
            },
            {
                name: "February",
                y: 30,
                drilldown: "2022-Maths-February"
            }
        ]
    },
    // Month-wise Day Data for Hindi 2021
    {
        name: "January 2021 Hindi",
        id: "2021-Hindi-January",
        data: Array.from({ length: 31 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    {
        name: "February 2021 Hindi",
        id: "2021-Hindi-February",
        data: Array.from({ length: 28 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    // Month-wise Day Data for English 2021
    {
        name: "January 2021 English",
        id: "2021-English-January",
        data: Array.from({ length: 31 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    {
        name: "February 2021 English",
        id: "2021-English-February",
        data: Array.from({ length: 28 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    // Month-wise Day Data for Maths 2021
    {
        name: "January 2021 Maths",
        id: "2021-Maths-January",
        data: Array.from({ length: 31 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    {
        name: "February 2021 Maths",
        id: "2021-Maths-February",
        data: Array.from({ length: 28 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    // Month-wise Day Data for Hindi 2022
    {
        name: "January 2022 Hindi",
        id: "2022-Hindi-January",
        data: Array.from({ length: 31 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    {
        name: "February 2022 Hindi",
        id: "2022-Hindi-February",
        data: Array.from({ length: 28 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    // Similar Data for English and Maths 2022...
    {
        name: "January 2022 English",
        id: "2022-English-January",
        data: Array.from({ length: 31 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    {
        name: "February 2022 English",
        id: "2022-English-February",
        data: Array.from({ length: 28 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    {
        name: "January 2022 Maths",
        id: "2022-Maths-January",
        data: Array.from({ length: 31 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    },
    {
        name: "February 2022 Maths",
        id: "2022-Maths-February",
        data: Array.from({ length: 28 }, (_, i) => ({
            name: `Day ${i + 1}`,
            y: Math.floor(Math.random() * 10) + 1
        }))
    }
];
