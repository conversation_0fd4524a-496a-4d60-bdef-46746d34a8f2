import * as Yup from "yup";
import checkout from "./form";

const {
  formField: {
    name,
    totalScore,
    class: classField,
    subjectName,
    questions,
    sectionList,
  },
} = checkout;

const questionSchema = Yup.object().shape({
  questionNumber: Yup.number()
    .required("Question number is required")
    .min(1, "Question number must start from 1"),
  question: Yup.string().required("Question is required"),
  questionScore: Yup.number()
    .required("Question score is required")
    .min(1, "Question score must start from 1"),
  questionRubric: Yup.string().required("Question rubric is required"),
});

const validations = [
  Yup.object().shape({
    [name.name]: Yup.string()
      .required(name.errorMsg)
      .min(5, "Assignment Name must be at least 5 characters")
      .max(100, "Assignment Name must not exceed 100 characters"),
    [totalScore.name]: Yup.number()
      .required(totalScore.errorMsg)
      .min(0, "Total score must be at least 0")
      .max(1000, "Total score must not exceed 1000"),
    [classField.name]: Yup.number()
      .required(classField.errorMsg)
      .min(6, "Grade must be at least 6")
      .max(12, "Grade must not exceed 12"),
    [subjectName.name]: Yup.string().required(subjectName.errorMsg),
    [sectionList.name]: Yup.array()
      .of(Yup.string())
      .min(1, sectionList.errorMsg),
  }),
  Yup.object().shape({
    [questions.name]: Yup.array().of(questionSchema).min(1, questions.errorMsg),
  }),
];

export default validations;
