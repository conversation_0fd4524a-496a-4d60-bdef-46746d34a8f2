import React, { useContext, useEffect } from "react";
import MDBox from "components/atoms/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import { ApiServiceContext } from "context";
import OverallStatsCardsWrapper from "./components/OverallStatsCardsWrapper";
import AssessmentsBySubjectChartWrapper from "./components/AssessmentsBySubjectChartWrapper";
import MonthlyAssessmentsChartWrapper from "./components/MonthlyAssessmentsChartWrapper";
import TopPerformingStudentsWrapper from "./components/TopPerformingStudentsWrapper";
import Grid from "@mui/material/Grid";
import AssignmentsBySectionsChart from "./components/AssignmentsBySectionsChart";
import StudentsBySectionsTable from "./components/StudentsBySectionsTable";

function Classes({ grade }) {
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);

  useEffect(() => {
    if (!loading && !apiService) {
      handleLogin(window.location.href);
    }
  }, [apiService, loading]);

  return (
    <DashboardLayout>
      <DashboardNavbar miniSidenav={true} breadCrumbText={`Grade ${grade}`} />
      <MDBox py={1} mb={3}>
        <MDBox mb={3}>
          <OverallStatsCardsWrapper grade={grade} />
        </MDBox>
        <Grid container spacing={3} mb={3}>
          <AssessmentsBySubjectChartWrapper grade={grade} />
          <MonthlyAssessmentsChartWrapper grade={grade} />
        </Grid>

        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} lg={9}>
            <AssignmentsBySectionsChart grade={grade} />
          </Grid>
          <Grid item xs={12} lg={3}>
            <StudentsBySectionsTable grade={grade} />
          </Grid>
        </Grid>

        <TopPerformingStudentsWrapper grade={grade} />
      </MDBox>
    </DashboardLayout>
  );
}

export default Classes;
