import React from "react";
import { Card, Grid, Skeleton } from "@mui/material";
import MDBox from "components/atoms/MDBox";

const BarChartsSkeleton = () => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <MDBox p={3} sx={{ minHeight: "23rem", maxHeight: "23rem" }}>
            <Skeleton animation="wave" width={200} height={30} />
            <Skeleton animation="wave" width="100%" height={250} />
          </MDBox>
        </Card>
      </Grid>
    </Grid>
  );
};

export default BarChartsSkeleton;

