import React, { useState } from "react";
import MDBox from "components/atoms/MDBox";
import { LightBox } from "components/molecules/LightBox/LightBox";

export const ImageGallery = ({ imageUrls }) => {
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const openLightbox = (index) => {
    setSelectedImageIndex(index);
    setIsLightboxOpen(true);
  };

  return (
    <>
      <MDBox
        pt={2.5}
        gap={2}
        pb={2}
        sx={{
          display: "flex",
          alignItems: "center",
          overflowX: "auto",
          overflowY: "hidden",
          scrollbarWidth: "thin",
          scrollbarColor: "#444 #f1f1f1",
          mt: { xs: 7, lg: -2 },
        }}
      >
        {imageUrls.map((url, index) => (
          <img
            key={index}
            src={url}
            className="h-[100px] rounded-sm"
            alt={`Uploaded Image ${index + 1}`}
            onClick={() => openLightbox(index)}
            style={{ cursor: "pointer" }}
          />
        ))}
      </MDBox>

      {isLightboxOpen && (
        <LightBox
          previews={imageUrls}
          setIsLightboxOpen={setIsLightboxOpen}
          setPhotoIndex={setSelectedImageIndex}
          photoIndex={selectedImageIndex}
        />
      )}
    </>
  );
};
