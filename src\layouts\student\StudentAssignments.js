import React, { useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import MDBox from "components/atoms/MDBox";
import Card from "@mui/material/Card";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import { TableInfo } from "components/molecules/TableInfo/TableInfo";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import DataTable from "examples/Tables/DataTable";
import Loader from "components/atoms/Loader/Loader";
import { UploadGradeModal } from "components/molecules/UploadGradeModal/UploadGradeModal";

const StudentAssignmentTableHeader = [
  { Header: "Assignment Name", accessor: "assignmentName", display: true },
  { Header: "Assignment ID", accessor: "assignmentId", display: false },
  { Header: "Subject", accessor: "subject", display: true },
  { Header: "Total Score", accessor: "assignmentScore", display: true },
  { Header: "Achieved Score", accessor: "studentScore", display: true },
  { Header: "Status", accessor: "status", display: true },
];

function StudentAssignments() {
  const { apiService } = useContext(ApiServiceContext);
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(true);
  const [allStudents, setAllStudents] = useState([]);
  const [studentInfo, setStudentInfo] = useState(null);
  const [isGradeModalOpen, setIsGradeModalOpen] = useState(false);
  const [isUploadModalLoading, setIsUploadModalLoading] = useState(false);
  const [selectedAssignmentId, setSelectedAssignmentId] = useState(null);
  const [tableData, setTableData] = useState({
    columns: StudentAssignmentTableHeader,
    rows: [],
  });

  useEffect(() => {
    const fetchStudentInfo = async () => {
      try {
        const email = apiService?.userEmail;
        if (email) {
          const student = await apiService.getStudentByEmail(email);
          setStudentInfo(student);
        }
      } catch (err) {
        toast.error("Error fetching student information");
      }
    };

    fetchStudentInfo();
  }, [apiService]);

  useEffect(() => {
    const fetchAssignments = async () => {
      try {
        const result = await apiService.getAllSubmissions(
          null,
          apiService?.studentId
        );

        setTableData({
          columns: StudentAssignmentTableHeader,
          rows: result,
        });

        setAllStudents(result);
      } catch (err) {
        toast.error("Error fetching assignments");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssignments();
  }, [apiService]);

  const handleUploadSubmission = (studentData) => {
    const assignmentId = studentData?.row?.original?.assignmentId;
    if (!assignmentId) {
      toast.error("Invalid assignment selected");
      return;
    }
    setSelectedAssignmentId(assignmentId);
    setIsUploadModalLoading(true);
    setIsGradeModalOpen(true);
  };

  if (isLoading) {
    return <Loader fullScreen message="Loading assignments..." />;
  }

  return (
    <DashboardLayout>
      <DashboardNavbar breadCrumbText="Student Assignments" />
      {selectedAssignmentId && (
        <UploadGradeModal
          assignmentId={selectedAssignmentId}
          selectedStudentId={apiService?.studentId}
          setSelectedStudentId={() => {}}
          isGradeModalOpen={isGradeModalOpen}
          setIsGradeModalOpen={setIsGradeModalOpen}
          setAllStudents={setAllStudents}
          allStudents={allStudents}
          isUploadModalLoading={isUploadModalLoading}
          setIsUploadModalLoading={setIsUploadModalLoading}
          setTableData={setTableData}
          isStudentRole={true}
        />
      )}
      <MDBox py={1} mb={3}>
        <Card>
          <TableInfo
            tableTitle={`${studentInfo?.firstName} ${studentInfo?.lastName}`}
            isCustomTitle
            tableDesc={`Class ${studentInfo?.class || "~"} | Section ${
              studentInfo?.section || "~"
            } | Roll No. ${studentInfo?.rollNumber || "~"}`}
            isDescBold
          />
          <DataTable
            table={tableData}
            canSearch
            uploadSubmission
            onUploadSubmission={handleUploadSubmission}
            viewSubmission
            onViewSubmission={(submission) => {
              navigate(
                `/student/submission/${submission.row.values.assignmentId}?studentId=${apiService?.studentId}`
              );
            }}
          />
        </Card>
      </MDBox>
    </DashboardLayout>
  );
}

export default StudentAssignments;
