import React from "react";
import { Card, Grid, Skeleton } from "@mui/material";
import MDBox from "components/atoms/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";

const ClassesDashboardSkeleton = ({ grade }) => {
  return (
    <DashboardLayout>
      <DashboardNavbar miniSidenav={true} breadCrumbText={"Grade " + grade} />
      <MDBox py={1} mb={3}>
        {/* Stats Cards */}
        <MDBox mb={3}>
          <Grid container spacing={3}>
            {[1, 2, 3].map((item) => (
              <Grid item xs={12} sm={4} key={item}>
                <Card>
                  <MDBox p={2} display="flex" justifyContent="space-between">
                    <MDBox>
                      <Skeleton animation="wave" width={120} height={20} />
                      <Skeleton animation="wave" width={60} height={30} />
                    </MDBox>
                    <Skeleton
                      animation="wave"
                      variant="circular"
                      width={45}
                      height={45}
                    />
                  </MDBox>
                </Card>
              </Grid>
            ))}
          </Grid>
        </MDBox>

        {/* Charts Section */}
        <MDBox
          sx={{
            minHeight: { xs: "100%", md: "24.5rem" },
            mb: { xs: 3, md: 9 },
          }}
        >
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <MDBox p={3}>
                  <Skeleton animation="wave" width={200} height={30} />
                  <Skeleton animation="wave" width="100%" height={300} />
                </MDBox>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <MDBox p={3}>
                  <Skeleton animation="wave" width={200} height={30} />
                  <Skeleton animation="wave" width="100%" height={300} />
                </MDBox>
              </Card>
            </Grid>
          </Grid>
        </MDBox>

        {/* Bar Charts Section */}
        <MDBox mb={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <Card>
                <MDBox p={3}>
                  <Skeleton animation="wave" width={200} height={30} />
                  <Skeleton animation="wave" width="100%" height={250} />
                </MDBox>
              </Card>
            </Grid>
            <Grid item xs={12} lg={4}>
              <Card>
                <MDBox p={3}>
                  <Skeleton animation="wave" width={150} height={30} />
                  <Skeleton animation="wave" width="100%" height={250} />
                </MDBox>
              </Card>
            </Grid>
          </Grid>
        </MDBox>

        {/* Table Section */}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <MDBox pt={3} px={3}>
                <Skeleton animation="wave" width={200} height={30} />
              </MDBox>
              <MDBox py={1} px={3}>
                {[1, 2, 3, 4, 5].map((item) => (
                  <MDBox
                    key={item}
                    display="flex"
                    justifyContent="space-between"
                    alignItems="center"
                    py={2}
                  >
                    <Skeleton animation="wave" width="15%" height={20} />
                    <Skeleton animation="wave" width="15%" height={20} />
                    <Skeleton animation="wave" width="15%" height={20} />
                    <Skeleton animation="wave" width="15%" height={20} />
                    <Skeleton animation="wave" width="15%" height={20} />
                  </MDBox>
                ))}
              </MDBox>
            </Card>
          </Grid>
        </Grid>
      </MDBox>
    </DashboardLayout>
  );
};

export default ClassesDashboardSkeleton;