import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import Loader from "components/atoms/Loader/Loader";
import MDButton from "components/atoms/MDButton";

function DeleteInstituteModal({
  isDeleteModalOpen,
  setIsDeleteModalOpen,
  deleteLoader,
  handleDeleteInstitute,
}) {
  return (
    <Dialog
      open={isDeleteModalOpen}
      onClose={() => !deleteLoader && setIsDeleteModalOpen(false)}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <MDTypography variant="h5" color="error">
          Delete Institute
        </MDTypography>
      </DialogTitle>
      <DialogContent>
        <MDTypography variant="h6" color="dark">
          Are you sure you want to delete this institute? This action cannot be
          undone and will result in:
        </MDTypography>
        <MDBox mt={2}>
          <ul style={{ listStyleType: "disc", paddingLeft: "20px" }}>
            <li>
              <MDTypography variant="body3">
                Deletion of all associated data including assignments, students,
                and instructors
              </MDTypography>
            </li>
            <li>
              <MDTypography variant="body3">
                Removal of all user access and permissions related to this
                institute
              </MDTypography>
            </li>
            <li>
              <MDTypography variant="body3">
                Permanent loss of all academic records and submissions
              </MDTypography>
            </li>
          </ul>
        </MDBox>
        <MDBox mt={2}>
          <MDTypography variant="body2" fontWeight="bold" color="error">
            Please confirm that you understand the consequences of this action.
          </MDTypography>
        </MDBox>
      </DialogContent>
      <DialogActions>
        <MDBox
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          width="100%"
          px={0.5}
        >
          <MDButton
            variant="outlined"
            color="dark"
            onClick={() => setIsDeleteModalOpen(false)}
            disabled={deleteLoader}
          >
            Cancel
          </MDButton>
          <MDButton
            variant="gradient"
            color="error"
            onClick={handleDeleteInstitute}
            disabled={deleteLoader}
            sx={{ width: "10rem" }}
          >
            {deleteLoader ? <Loader size={20} /> : "Delete Institute"}
          </MDButton>
        </MDBox>
      </DialogActions>
    </Dialog>
  );
}

export default DeleteInstituteModal;

