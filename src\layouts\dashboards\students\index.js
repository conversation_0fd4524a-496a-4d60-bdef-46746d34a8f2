import React, { useState, useEffect, useContext } from "react";
import Card from "@mui/material/Card";
import MDBox from "components/atoms/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import DataTable from "examples/Tables/DataTable";
import { Modal } from "components/atoms/Modal/Modal";
import { toast } from "react-toastify";
import Loader from "components/atoms/Loader/Loader";
import { ApiServiceContext } from "context";
import { StudentTableHeader } from "constants/StudentTableHeader/StudentTableHeader";
import { TableInfo } from "components/molecules/TableInfo/TableInfo";
import { DeleteModal } from "components/molecules/DeleteModal/DeleteModal";
import { authenticateUser } from "utils/helperFunctions/authenticateUser";
import { StudentForm } from "components/organisms/StudentsForm/StudentForm";
import { useNavigate } from "react-router-dom";

export function Students() {
  const navigate = useNavigate();
  const [allStudents, setAllStudents] = useState([]);
  const [studentId, setStudentId] = useState();
  const [isLoading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tableData, setTableData] = useState({});
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [studentFormData, setStudentFormData] = useState({});
  const [formType, setFormType] = useState("edit");
  const [submitStudentLoader, setSubmitStudentLoader] = useState(false);
  const [deleteStudentLoader, setDeleteStudentLoader] = useState(false);
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);
  const authUser = authenticateUser(apiService, loading, handleLogin);
  useEffect(() => {
    authUser();
  }, [authUser]);

  useEffect(() => {
    const fetchStudents = async () => {
      try {
        const result = await apiService.getAllStudents();
        setAllStudents(result);
        setLoading(false);
      } catch (err) {
        setError(err);
      }
    };

    fetchStudents();
  }, [apiService]);

  useEffect(() => {
    setTableData({
      columns: StudentTableHeader,
      rows: allStudents,
    });
  }, [allStudents]);

  useEffect(() => {
    if (error) {
      toast.error("Something went wrong");
    }
  }, [error]);

  const handleEditStudentModal = (data) => {
    setIsModalOpen(true);
    setFormType("edit");
    // Ensure all fields including email are properly passed
    const studentData = {
      firstName: data?.row?.values?.firstName || "",
      lastName: data?.row?.values?.lastName || "",
      studentId: data?.row?.values?.studentId || "",
      rollNumber: data?.row?.values?.rollNumber || "",
      class: data?.row?.values?.class || "",
      section: data?.row?.values?.section || "",
      email: data?.row?.values?.email || "", // Add email field
    };
    setStudentFormData(studentData);
  };

  const handleDeleteStudent = async () => {
    setDeleteStudentLoader(true);
    try {
      await apiService.deleteStudent(studentId);
      setAllStudents(
        allStudents.filter((student) => student.studentId !== studentId)
      );
      toast.success("Student Deleted Successfully");
    } catch (err) {
      setError(err);
    } finally {
      setDeleteStudentLoader(false);
      setIsDeleteModalOpen(false);
    }
  };

  const handleFormSubmit = async (formData) => {
    setSubmitStudentLoader(true);
    try {
      if (formType === "edit") {
        await apiService.editStudent(formData);
      } else {
        await apiService.createNewStudent(formData);
      }
      if (formType !== "edit") {
        setAllStudents([...allStudents, formData]);
      } else {
        setAllStudents(
          allStudents.map((student) =>
            student.studentId === formData.studentId ? formData : student
          )
        );
      }
      formType === "edit"
        ? toast.success("Student edited successfully")
        : toast.success("Student added successfully");
    } catch (err) {
      setError(err);
      toast.error("Student already exist");
    } finally {
      setSubmitStudentLoader(false);
      setIsModalOpen(false);
    }
  };

  const handleAddStudentModal = () => {
    setIsModalOpen(true);
    setFormType("add");
    setStudentFormData({});
  };

  const handleDeleteStudentModal = (studentData) => {
    setIsDeleteModalOpen(true);
    setStudentId(studentData?.row?.values?.studentId);
  };

  const handleRowClick = (data) => {
    navigate(`/students/${data?.studentId}`);
  };

  if (isLoading) {
    return <Loader fullScreen message="Don't Reload Page" />;
  }

  return (
    <DashboardLayout>
      <DashboardNavbar breadCrumbText="All Students" title="Students" />
      <DeleteModal
        isModalOpen={isDeleteModalOpen}
        setIsModalOpen={setIsDeleteModalOpen}
        handleChange={handleDeleteStudent}
        title="Delete Student"
        loader={deleteStudentLoader}
      />
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        headingText={
          formType === "edit" ? "Edit Student Data" : "Add Student Data"
        }
      >
        <StudentForm
          handleFormSubmit={handleFormSubmit}
          studentData={studentFormData}
          submitStudentLoader={submitStudentLoader}
          formType={formType}
        />
      </Modal>
      <MDBox py={1}>
        <MDBox mb={3}>
          <Card>
            <TableInfo
              tableTitle="Students Data"
              tableDesc="List of all Students"
              buttonText="Add New Student"
              handleClick={handleAddStudentModal}
            />
            <DataTable
              table={tableData}
              enableFilter
              editData
              deleteData
              onEdit={handleEditStudentModal}
              onDelete={handleDeleteStudentModal}
              isRowClickable={false}
              handleRowClick={handleRowClick}
            />
          </Card>
        </MDBox>
      </MDBox>
    </DashboardLayout>
  );
}
