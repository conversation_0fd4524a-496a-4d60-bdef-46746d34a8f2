import * as pdfjsLib from "pdfjs-dist/legacy/build/pdf";
import pdfjsWorker from "pdfjs-dist/legacy/build/pdf.worker.entry";

// Set worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

export const convertPdfToImages = async (pdfFile) => {
  const pdf = await pdfjsLib.getDocument(URL.createObjectURL(pdfFile)).promise;
  const imageBlobs = [];

  for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
    const page = await pdf.getPage(pageNum);
    const viewport = page.getViewport({ scale: 2 });
    const canvas = document.createElement("canvas");
    canvas.height = viewport.height;
    canvas.width = viewport.width;
    const context = canvas.getContext("2d");
    await page.render({ canvasContext: context, viewport }).promise;
    const imageBlob = await new Promise((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob);
      });
    });
    const imageFile = new File([imageBlob], `page-${pageNum}.png`, {
      type: "image/png",
    });
    imageBlobs.push(imageFile);
  }
  return imageBlobs;
};
