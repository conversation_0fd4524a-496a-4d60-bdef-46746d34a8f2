import React, { forwardRef } from "react";
import PropTypes from "prop-types";
import { ErrorMessage, Field } from "formik";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import MDInput from "components/atoms/MDInput";

const FormField = forwardRef(
  (
    {
      label,
      name,
      multiline = false,
      rows = 4,
      max,
      min = 0,
      step,
      type = "text",
      disabled = false,
      isFixedHeading = false,
      ...rest
    },
    ref
  ) =>
    !isFixedHeading ? (
      <MDBox mb={1.5}>
        <Field
          {...rest}
          name={name}
          as={MDInput}
          variant="standard"
          label={label}
          fullWidth
          multiline={multiline}
          rows={multiline && !disabled ? rows : undefined}
          type={type}
          inputProps={{ max, min, step, sx: { pr: 3 } }}
          disabled={disabled}
          inputRef={ref}
          onWheel={(e) => e.target.blur()}
        />
        <MDBox mt={0.75}>
          <MDTypography
            component="div"
            variant="caption"
            color="error"
            fontWeight="regular"
          >
            <ErrorMessage name={name} />
          </MDTypography>
        </MDBox>
      </MDBox>
    ) : (
      <MDBox mb={1.5}>
        <Field
          {...rest}
          name={name}
          as={MDInput}
          variant="standard"
          label={label}
          fullWidth
          multiline={multiline}
          rows={multiline && !disabled ? rows : undefined}
          type={type}
          inputProps={{ max, min, step, sx: { pr: 3 } }}
          disabled={disabled}
          inputRef={ref}
          InputLabelProps={{
            shrink: true,
          }}
          onWheel={(e) => e.target.blur()}
        />
        <MDBox mt={0.75}>
          <MDTypography
            component="div"
            variant="caption"
            color="error"
            fontWeight="regular"
          >
            <ErrorMessage name={name} />
          </MDTypography>
        </MDBox>
      </MDBox>
    )
);

FormField.propTypes = {
  label: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  multiline: PropTypes.bool,
  rows: PropTypes.number,
  max: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  min: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  step: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  type: PropTypes.string,
  disabled: PropTypes.bool,
};

export default FormField;
