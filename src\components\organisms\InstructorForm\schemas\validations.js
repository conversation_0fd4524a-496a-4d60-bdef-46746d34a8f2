import * as Yup from "yup";
import form from "./form";

const {
  formField: { firstName, lastName, email },
} = form;

const validations = Yup.object().shape({
  [firstName.name]: Yup.string().required(firstName.errorMsg),
  [lastName.name]: Yup.string().required(lastName.errorMsg),
  [email.name]: Yup.string()
    .email("Invalid email address")
    .matches(
      /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/,
      "Email must be lowercase"
    )
    .required(email.errorMsg),
});

export default validations;
