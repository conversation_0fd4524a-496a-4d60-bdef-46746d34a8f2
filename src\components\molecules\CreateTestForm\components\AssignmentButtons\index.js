import React, { useEffect, useState, useCallback } from "react";
import MDButton from "components/atoms/MDButton";
import Loader from "components/atoms/Loader/Loader";
import { toast } from "react-toastify";
import MDBox from "components/atoms/MDBox";

const AssignmentButtons = ({
  activeStep,
  formType,
  isSubmitting,
  handleBack,
  setActiveStep,
  errors,
  handleViewAssignment,
  isRubricGenerated,
  handleNavigateToGradeAssignment,
  values,
  setIsFormErrors,
  isShowRubricScreen,
  setIsShowRubricScreen,
  scrollToTop,
  generateRubricChecks,
  hasRubricErrors,
  setHasRubricErrors,
  isValuesChanged,
  isRubricButtonClicked,
  setIsRubricButtonClicked,
  isRubricFromFiles,
  setIsRubricFromFiles,
  isAutoFillAssignment,
  setIsAutoFillAssignment,
}) => {
  const [fieldErrors, setFieldErrors] = useState(0);
  const [isCreateClicked, setIsCreateClicked] = useState(false);

  // Use a memoized function to calculate field errors to prevent unnecessary re-renders
  const calculateFieldErrors = useCallback(() => {
    if (!errors) return 0;

    let assignInfoErrors = Object.keys(errors)?.length || 0;

    let questionErrors = 0;

    (values.questions || []).forEach((item) => {
      if (item.questionNumber === 0) questionErrors++;
      if (item.questionScore === 0) questionErrors++;
      if (item.question === "") questionErrors++;
      if (isCreateClicked && activeStep === 1 && item.questionRubric === "") {
        setHasRubricErrors(true);
        questionErrors++; // Explicit check on rubric step
      }
    });

    // Only update if the state changes
    if (isCreateClicked && questionErrors === 0) {
      setHasRubricErrors(false);
      setIsCreateClicked(false);
      setIsFormErrors(false);
      setFieldErrors(0);
      assignInfoErrors = 0;
      questionErrors = 0;
    }

    return assignInfoErrors + questionErrors;
  }, [errors, values, isCreateClicked, activeStep]);

  useEffect(() => {
    const hasErrors = calculateFieldErrors();
    setFieldErrors(hasErrors);

    if (hasErrors > 0) {
      setIsFormErrors(true);
    } else {
      setIsFormErrors(false);
    }
  }, [calculateFieldErrors]);

  const renderButton = (condition, props) => {
    if (condition) {
      return <MDButton {...props} />;
    }
    return null;
  };

  const handleCreateAssignmentClick = () => {
    !isCreateClicked && setIsCreateClicked(true);

    if (fieldErrors > 0) {
      toast.error("Please fill all the required fields !");
      setIsFormErrors(true);
      return;
    } else {
      setIsFormErrors(false);
    }
  };

  const getJustifyContent = () => {
    if (activeStep === 0) return "flex-end";
    if (formType === "view" && activeStep === 1) {
      return "flex-start";
    }
    return "space-between";
  };

  return (
    <MDBox
      width="100%"
      display="flex"
      alignItems="center"
      justifyContent={getJustifyContent()}
    >
      {renderButton(
        (formType === "add" && activeStep === 1) ||
          (formType === "view" && activeStep === 2),
        {
          variant: "gradient",
          color: "light",
          onClick: () => {
            if (isShowRubricScreen) {
              setIsShowRubricScreen(false);
              isRubricFromFiles && setIsRubricFromFiles(false);
            }

            if (isRubricButtonClicked && activeStep === 1) {
              setIsRubricButtonClicked(false);
              hasRubricErrors && setHasRubricErrors(false);
              isCreateClicked && setIsCreateClicked(false);
              setIsFormErrors(false);
              setFieldErrors(0);
            }

            if (formType === "view" && activeStep === 2) {
              setActiveStep(0);
            } else {
              handleBack();
            }

            if (formType === "view" && activeStep === 0) {
              scrollToTop();
            }
          },
          children: "Back",
        }
      )}

      {renderButton(
        (formType === "add" && activeStep === 0 && !isShowRubricScreen) ||
          (activeStep === 0 && formType === "view"),
        {
          disabled: isSubmitting,
          variant: "gradient",
          color: "dark",
          type: "button",
          onClick: () => {
            if (formType === "add") {
              const hasErrorsFound = generateRubricChecks(values);
              if (hasErrorsFound) return;

              if (activeStep === 0 && isAutoFillAssignment) {
                setIsAutoFillAssignment(false);
              }

              if (isRubricGenerated && !isValuesChanged) {
                setActiveStep(1);
                setIsRubricButtonClicked(true);
              } else {
                setActiveStep(1);
                setIsShowRubricScreen(true);
              }
            } else if (formType === "view") {
              setActiveStep(2);
              scrollToTop();
            }
          },
          children: "Next",
        }
      )}

      {renderButton(
        (activeStep === 0 && formType === "edit") ||
          (activeStep === 1 && formType === "add" && isRubricButtonClicked),
        {
          disabled: isSubmitting,
          type: "submit",
          variant: "gradient",
          color: "dark",
          sx: {
            width: "6rem",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          },
          children: isSubmitting ? (
            <Loader loaderColor="white" />
          ) : formType === "edit" ? (
            "Save"
          ) : (
            "Create"
          ),
          onClick: handleCreateAssignmentClick,
        }
      )}

      {renderButton(activeStep === 2 && formType === "add", {
        variant: "gradient",
        color: "light",
        onClick: handleViewAssignment,
        children: "View Assignment",
      })}

      {renderButton(activeStep === 2 && formType === "add", {
        variant: "gradient",
        color: "secondary",
        onClick: handleNavigateToGradeAssignment,
        children: "Grade Assignment",
      })}
    </MDBox>
  );
};

export default AssignmentButtons;
