import React from "react";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import { HorizontalTabs } from "components/atoms/HorizontalTabs/HorizontalTabs";
import AcademicTermsSettings from "components/organisms/AcademicTermsSettings/AcademicTermsSettings";
import ManageSectionsSettings from "components/organisms/ManageSectionsSettings/ManageSectionsSettings";

function AdvancedSettings() {
  const tabs = [
    {
      label: "Academic Terms",
      content: <AcademicTermsSettings />,
    },
    {
      label: "Manage Sections",
      content: <ManageSectionsSettings />,
    },
  ];

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <HorizontalTabs tabs={tabs} stickyNavbar />
    </DashboardLayout>
  );
}

export default AdvancedSettings;
