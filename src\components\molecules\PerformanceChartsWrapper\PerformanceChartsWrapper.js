import React, { useCallback, useEffect, useRef } from "react";
import TopicWisePerformanceChart from "../TopicWisePerformanceChart/TopicWisePerformanceChart";
import DownloadSubmissionPerformanceSpiderChart from "../DownloadSubmissionPerformanceSpiderChart/DownloadSubmissionPerformanceSpiderChart";

const PerformanceChartsWrapper = ({ data, onRenderComplete }) => {
  const isRendered = useRef(false); // Use a ref to track rendering

  const handleRenderComplete = useCallback(() => {
    if (onRenderComplete) {
      onRenderComplete();
    }
  }, [onRenderComplete]);

  useEffect(() => {
    // Check if this is the first render
    if (!isRendered.current) {
      isRendered.current = true; // Mark as rendered
    } else {
      handleRenderComplete(); // Call the completion handler
    }
  }, [handleRenderComplete]);
  return (
    <div
      style={{
        marginTop: "20px",
        width: "100%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
      }}
    >
      <div
        style={{
          width: "750px",
        }}
      >
        <TopicWisePerformanceChart data={data} />
        <DownloadSubmissionPerformanceSpiderChart data={data} />
      </div>
    </div>
  );
};

export default PerformanceChartsWrapper;
