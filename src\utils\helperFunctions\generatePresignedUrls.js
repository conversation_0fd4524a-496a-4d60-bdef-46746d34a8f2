import AWS from "aws-sdk";

AWS.config.update({
  accessKeyId: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_ACCESS_KEY,
  secretAccessKey: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_SECRET_KEY,
  region: process.env.REACT_APP_AWS_REGION,
});

const s3 = new AWS.S3();

export const generatePresignedUrls = (imageUrls) => {
  if (!imageUrls) return;
  const presignedUrls = imageUrls.map((imageKey) => {
    const params = {
      Bucket: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_BUCKET,
      Key: imageKey,
      Expires: 60,
    };
    return s3.getSignedUrl("getObject", params);
  });
  return presignedUrls;
};
