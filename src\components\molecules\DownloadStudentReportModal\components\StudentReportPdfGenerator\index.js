import React, { useRef, useEffect, useState } from "react";
import html2pdf from "html2pdf.js";
import "./StudentReportPdfGenerator.css";
import logo from "assets/images/logo-eddyowl.png";
import DownloadStudentPerformanceSpiderChartWrapper from "../StudentReportPdfGeneratorWrapper/StudentReportPdfGeneratorWrapper";
import studentReportData from "../../data/studentReportData";

function StudentReportPdfGenerator({
  triggerDownload,
  data,
  onRenderComplete,
}) {
  const printRef = useRef();
  const [chartsRenderedCount, setChartsRenderedCount] = useState(0);
  const totalCharts = data?.stats?.length;

  const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  const waitSleep = async () => {
    await sleep(1000);
  };

  useEffect(() => {
    if (triggerDownload && chartsRenderedCount === totalCharts) {
      waitSleep().then(() => generatePDF());
    }
  }, [triggerDownload, chartsRenderedCount, totalCharts]);

  const handleSingleChartRenderComplete = () => {
    setChartsRenderedCount((prevCount) => prevCount + 1);
  };

  const generatePDF = () => {
    const element = printRef.current;

    const opt = {
      margin: [0.2, 0.5, 0.2, 0.5],
      filename: `${data.name}-report.pdf`,
      image: { type: "jpeg", quality: 0.98 },
      html2canvas: { scale: 2, useCORS: true },
      jsPDF: { unit: "in", format: "letter", orientation: "portrait" },
      pagebreak: { mode: ["avoid-all", "css", "legacy"] },
    };

    html2pdf()
      .from(element)
      .set(opt)
      .save()
      .then(() => {
        if (onRenderComplete) {
          onRenderComplete();
        }
      });
  };

  // Function to get chapters with their average score based on topics
  const getChaptersWithAverageScore = (stat) => {
    const chapterScores = new Map();

    // Aggregate scores and counts in a single pass
    stat.tests.forEach((test) => {
      test.chapters.forEach(({ chapter, topics }) => {
        let chapterData = chapterScores.get(chapter) || {
          totalScore: 0,
          count: 0,
        };

        // Accumulate scores and counts for the chapter
        topics.forEach(({ score }) => {
          chapterData.totalScore += score;
          chapterData.count += 1;
        });

        chapterScores.set(chapter, chapterData);
      });
    });

    // Convert aggregated scores to an array of objects with average scores
    return Array.from(chapterScores, ([name, { totalScore, count }]) => ({
      name,
      studentAverageScore:
        count > 0 ? parseFloat((totalScore / count).toFixed(1)) : 0,
    }));
  };

  return (
    <div
      ref={printRef}
      style={{
        padding: "10px",
        fontSize: "18px",
        fontFamily: "sans-serif",
        backgroundColor: "white",
        overflow: "visible",
      }}
    >
      <div className="page-header">
        <img src={logo} alt="Company Logo" className="logo" />
        <div>
          <p>
            <b>Student ID:</b> {data?.studentId ?? "NA"}
          </p>
          <p>
            <b>Period:</b> {data?.period ?? "NA"}
          </p>
        </div>
      </div>

      {/* Student Details */}
      <div className="student-details">
        <div>
          <p className="student-name">{data?.name ?? "NA"}</p>

          <b>{`${data?.class ?? "NA"} (${data?.section ?? "NA"})`}</b>
        </div>

        <p>
          <b>Roll Number:</b> {data?.rollNumber ?? "NA"}
        </p>
      </div>
      <br />
      <hr className="divider-small" />
      <br />

      {/* Student Overall Performance */}
      <table className="table">
        <thead>
          <tr className="table-header">
            <td
              style={{
                width: "5rem",
              }}
              className="table-cell text-center"
            >
              Sr No.
            </td>
            <td className="table-cell">Subjects</td>

            <td
              style={{
                width: "12rem",
              }}
              className="table-cell text-center"
            >
              Score (%)
            </td>
            <td
              style={{
                width: "12rem",
              }}
              className="table-cell text-center"
            >
              Grade Avg (%)
            </td>
          </tr>
        </thead>
        <tbody>
          {data.stats?.map((subData, subIdx) => (
            <tr key={subIdx}>
              <td className="table-cell text-center">{subIdx + 1}.</td>
              <td className="table-cell">{subData?.subject}</td>

              <td className="table-cell text-center">
                {subData?.studentAverageScore}
              </td>
              <td className="table-cell text-center">
                {subData?.classAverageScore}
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Subject-wise report */}
      <div className="avoid-page-break">
        {data?.stats?.map((stat, statIdx) => {
          const chaptersWithAverageScore = getChaptersWithAverageScore(stat);

          return (
            <div
              style={{ marginTop: "120px" }}
              key={statIdx}
              className="page-break-before"
            >
              <div>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <b>{stat?.subject ?? "NA"}</b>
                  <p>
                    <b>Score:</b> {stat?.studentAverageScore ?? "NA"}%
                  </p>
                </div>
                <hr className="divider-small" />
                <p
                  style={{
                    marginLeft: "auto",
                    width: "150px",
                  }}
                >
                  <b>Grade Mean:</b> {stat?.classAverageScore ?? "NA"}%
                </p>
              </div>
              <br />

              {/* All Tests within a subject */}
              <table className="table">
                <thead>
                  <tr className="table-header">
                    <td
                      style={{
                        width: "5rem",
                      }}
                      className="table-cell text-center"
                    >
                      Sr No.
                    </td>
                    <td className="table-cell">Test</td>
                    <td
                      style={{
                        width: "9rem",
                      }}
                      className="table-cell text-center"
                    >
                      Score
                    </td>
                    <td
                      style={{
                        width: "9rem",
                      }}
                      className="table-cell text-center"
                    >
                      Grade Avg
                    </td>
                  </tr>
                </thead>
                <tbody>
                  {stat?.tests?.map((test, testIdx) => (
                    <tr key={testIdx}>
                      <td className="table-cell text-center">{testIdx + 1}.</td>
                      <td className="table-cell">{test?.name}</td>
                      <td className="table-cell text-center">
                        {test?.achievedScore}
                      </td>
                      <td className="table-cell text-center">
                        {test?.classAverageScore}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              <br />

              {/* Chapter-wise report */}
              <table className="table">
                <thead>
                  <tr className="table-header">
                    <td
                      style={{
                        width: "5rem",
                      }}
                      className="table-cell text-center"
                    >
                      Sr No.
                    </td>
                    <td className="table-cell">Chapter</td>
                    <td
                      style={{
                        width: "9rem",
                      }}
                      className="table-cell text-center"
                    >
                      Score
                    </td>
                  </tr>
                </thead>
                <tbody>
                  {chaptersWithAverageScore?.map((chapter, chapterIdx) => (
                    <tr key={chapterIdx}>
                      <td className="table-cell text-center">
                        {chapterIdx + 1}.
                      </td>
                      <td className="table-cell ">{chapter?.name}</td>
                      <td className="table-cell text-center">
                        {chapter?.studentAverageScore}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              <hr
                style={{
                  marginTop: "30px",
                  marginBottom: "10px",
                  border: "0.5px solid #b2b2b2",
                }}
              />

              {/* Render Chart using the Wrapper Component */}
              <DownloadStudentPerformanceSpiderChartWrapper
                subject={stat.subject}
                data={studentReportData}
                onRenderComplete={handleSingleChartRenderComplete}
              />

              <hr className="divider" />
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default StudentReportPdfGenerator;
