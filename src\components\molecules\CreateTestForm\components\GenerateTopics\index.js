import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import SuccessTickIcon from "assets/images/success-tick.png";
import { Autocomplete, Card, TextField } from "@mui/material";
import AiFeaturesIcon from "assets/images/ai-features.svg";
import MDButton from "components/atoms/MDButton";
import Loader from "components/atoms/Loader/Loader";

export function GenerateTopics({
  formType,
  isTopicsGenerated,
  topicsData,
  generateTopics,
  isTopicsLoading,
  setIsTopicsLoading,
}) {
  return !isTopicsGenerated ? (
    <MDBox
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="calc(100vh - 18rem)"
      mt={-8}
    >
      <MDBox display="flex" flexDirection="column" alignItems="center">
        <img src={SuccessTickIcon} alt="AI" height={60} width={60} />
        {formType === "add" && (
          <MDTypography
            fontWeight="regular"
            mt={2}
            fontSize={{ xs: "18px", md: "20px" }}
          >
            Assignment created successfully
          </MDTypography>
        )}
        <MDTypography
          variant="h6"
          fontWeight={"medium"}
          mt={formType === "add" ? 2 : 3}
          px={{ xs: 2, md: 0 }}
        >
          Gain insights on students performance by generating topics for this
          assignment !
        </MDTypography>
      </MDBox>
      <MDBox display="flex" alignItems="center" justifyContent="center" mt={4}>
        <MDButton
          disabled={isTopicsLoading}
          variant="outlined"
          color="warning"
          onClick={() => {
            setIsTopicsLoading(true);
            generateTopics();
          }}
          sx={{
            width: "12rem",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {isTopicsLoading ? (
            <Loader loaderColor="warning" />
          ) : (
            <>
              <img
                src={AiFeaturesIcon}
                alt="AI"
                height={20}
                width={20}
                style={{ marginRight: "10px" }}
              />
              <p>Generate Topics</p>
            </>
          )}
        </MDButton>
      </MDBox>
    </MDBox>
  ) : (
    <MDBox display="flex" flexDirection="column" gap={1} py={3} px={5}>
      <MDTypography variant="h5" fontWeight="bold">
        Topics
      </MDTypography>
      <MDBox mb={"5rem"}>
        {topicsData?.questions?.map((question, index) => (
          <MDBox key={index} mt={1}>
            <MDBox display="flex" alignItems="start" gap={2}>
              <MDTypography variant="h6" fontWeight="medium">
                Q.{question?.questionNumber}
              </MDTypography>
              <MDTypography variant="h6" fontWeight="regular">
                {question.question}
              </MDTypography>
            </MDBox>
            <MDBox mt={2}>
              {question.topics?.map((topicGroup, index) => {
                const topicList = topicGroup?.topics?.map((tp) => tp);
                return (
                  <Card
                    key={index}
                    sx={{
                      margin: "30px 0",
                      padding: "10px",
                      bgcolor: "#f1f2f6",
                    }}
                  >
                    <MDBox
                      display="flex"
                      flexDirection={{
                        xs: "column",
                        md: "row",
                      }}
                      alignItems="start"
                      justifyContent="space-between"
                      width="100%"
                    >
                      {/* card left side */}
                      <MDBox display="flex" alignItems="start" gap={1} p={1}>
                        <MDTypography
                          variant="h6"
                          fontWeight="regular"
                          sx={{ textWrap: "nowrap" }}
                        >
                          {`Chapter${
                            question?.topics?.length > 1
                              ? " " + (index + 1)
                              : ""
                          }: `}
                        </MDTypography>
                        <MDTypography
                          variant="h6"
                          fontWeight="medium"
                          sx={{ textWrap: "word-break" }}
                        >
                          {topicGroup.chapter}
                        </MDTypography>
                      </MDBox>

                      {/* card right side */}
                      <MDBox
                        width={"100%"}
                        sx={{
                          borderRadius: "10px !important",
                          width: { xs: "100%", md: "50%" },
                          maxWidth: "400px",
                          "& .MuiPaper-root:first-of-type": {
                            borderRadius: "10px !important",
                          },
                          "& .MuiPaper-root:last-of-type": {
                            borderRadius: "10px !important",
                          },
                        }}
                      >
                        <Card>
                          <MDBox
                            sx={{
                              overflow: "hidden",
                              width: "100%",
                              position: "relative",
                            }}
                          >
                            <Autocomplete
                              multiple
                              options={topicList}
                              getOptionLabel={(option) => option}
                              value={topicList || []}
                              className="assignment_dropdown"
                              disabled={true}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  sx={{
                                    width: "100%",
                                    "& .MuiInputBase-root": {
                                      overflow: "hidden",
                                      whiteSpace: "nowrap",
                                      textOverflow: "ellipsis",
                                    },
                                  }}
                                />
                              )}
                            />
                          </MDBox>
                        </Card>
                      </MDBox>
                    </MDBox>
                  </Card>
                );
              })}
            </MDBox>
            <hr
              style={{
                marginTop: "30px",
                marginBottom: "30px",
                height: "2px",
                borderBottom: "1px solid black",
              }}
            />
          </MDBox>
        ))}
      </MDBox>
    </MDBox>
  );
}
