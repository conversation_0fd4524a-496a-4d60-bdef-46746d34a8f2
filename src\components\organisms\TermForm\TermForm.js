import React from "react";
import { Formik, Form } from "formik";
import { Grid, TextField } from "@mui/material";
import { useLocation } from "react-router-dom";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import MDButton from "components/atoms/MDButton";
import Loader from "components/atoms/Loader/Loader";
import form from "components/organisms/TermForm/schemas/form";
import initialValues from "components/organisms/TermForm/schemas/initialValues";
import validations from "components/organisms/TermForm/schemas/validations";
import FormikDatePicker from "components/atoms/FormikDatePicker/FormikDatePicker.js";

// Enable the 'isBetween' plugin
dayjs.extend(isBetween);

function TermForm({
  onSubmit,
  initialValues: propInitialValues,
  termFormMode,
}) {
  const { formId } = form;
  const { name, startDate, endDate } = form.formField;
  const location = useLocation();
  const isAdvancedSettings = location.pathname === "/settings/advanced";

  const handleFormSubmit = (values, actions) => {
    const formattedValues = {
      ...values,
      startDate: dayjs(values.startDate),
      endDate: dayjs(values.endDate),
    };
    onSubmit(formattedValues, actions);
  };

  return (
    <Formik
      initialValues={propInitialValues || initialValues}
      validationSchema={validations[0]}
      onSubmit={handleFormSubmit}
      enableReinitialize
    >
      {({
        handleChange,
        handleBlur,
        values,
        errors,
        touched,
        isSubmitting,
      }) => (
        <Form id={formId} autoComplete="off">
          <MDBox mb={3}>
            {!isAdvancedSettings && (
              <MDTypography variant="h5" fontWeight="bold" color="dark">
                {termFormMode === "view"
                  ? "Add Your First Term Details"
                  : termFormMode === "edit"
                  ? "Edit Term Details"
                  : "Add Term Details"}
              </MDTypography>
            )}
            <MDTypography variant="body2" color="dark">
              {termFormMode === "view"
                ? "Specify the first term information for your institute"
                : termFormMode === "edit"
                ? "Update the details of the selected term"
                : "Add additional term information for your institute"}
            </MDTypography>
          </MDBox>

          <Grid container spacing={5}>
            {/* Term Name Field */}
            <Grid item xs={12}>
              <TextField
                label={name.label}
                name={name.name}
                value={values[name.name]}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched[name.name] && Boolean(errors[name.name])}
                helperText={touched[name.name] && errors[name.name]}
                fullWidth
                required
              />
            </Grid>

            {/* Date Pickers */}
            <Grid item xs={12} md={6}>
              <FormikDatePicker name={startDate.name} label={startDate.label} />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormikDatePicker name={endDate.name} label={endDate.label} />
            </Grid>
          </Grid>

          <MDTypography
            fontWeight="light"
            variant="button"
            className="flex justify-between p-1 pt-5"
          >
            Fields with * are mandatory
          </MDTypography>

          <MDBox
            sx={
              isAdvancedSettings
                ? { mt: 2, display: "flex", justifyContent: "flex-end" }
                : { position: "absolute", bottom: 25, right: 25 }
            }
          >
            <MDButton
              type="submit"
              variant="gradient"
              color="dark"
              disabled={isAdvancedSettings && isSubmitting}
              sx={{ width: "9rem" }}
            >
              {isAdvancedSettings && isSubmitting ? (
                <Loader size={20} color="white" />
              ) : termFormMode === "edit" ? (
                "Update Term"
              ) : (
                "Add Term"
              )}
            </MDButton>
          </MDBox>
        </Form>
      )}
    </Formik>
  );
}

export default TermForm;
