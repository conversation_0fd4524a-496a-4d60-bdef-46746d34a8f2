import { useState, useEffect } from "react";
import { generatePresignedUrls } from "utils/helperFunctions/generatePresignedUrls";

export const useImageUrls = (imageIdList) => {
  const [imageUrls, setImageUrls] = useState([]);

  useEffect(() => {
    if (imageIdList && imageIdList.length > 0) {
      const presignedUrls = generatePresignedUrls(imageIdList);
      setImageUrls(presignedUrls);
    }
  }, [imageIdList]);

  return imageUrls;
};
