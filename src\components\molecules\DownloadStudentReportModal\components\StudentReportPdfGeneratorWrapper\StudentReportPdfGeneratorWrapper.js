import React, { useEffect, useCallback, useRef } from "react";
import { DownloadStudentPerformanceSpiderChart } from "components/molecules/StudentsCharts/DownloadStudentPerformanceSpiderChart";

const DownloadStudentPerformanceSpiderChartWrapper = ({
  subject,
  data,
  onRenderComplete,
}) => {
  const isRendered = useRef(false); // Use a ref to track rendering

  const handleRenderComplete = useCallback(() => {
    if (onRenderComplete) {
      onRenderComplete();
    }
  }, [onRenderComplete]);

  useEffect(() => {
    // Check if this is the first render
    if (!isRendered.current) {
      isRendered.current = true; // Mark as rendered
    } else {
      handleRenderComplete(); // Call the completion handler
    }
  }, [handleRenderComplete]);

  return (
    <div
      style={{
        width: "100%",
        height: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <DownloadStudentPerformanceSpiderChart subject={subject} data={data} />
    </div>
  );
};

export default DownloadStudentPerformanceSpiderChartWrapper;