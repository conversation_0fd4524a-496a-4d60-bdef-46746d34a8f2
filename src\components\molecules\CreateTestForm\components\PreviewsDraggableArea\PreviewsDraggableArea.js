import React from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
  SortableContext,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import Loader from "components/atoms/Loader/Loader";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import CloseIcon from "@mui/icons-material/Close";

const SortablePreviewItem = ({
  preview,
  index,
  handleRemoveFile,
  openLightbox,
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: index });

  // Ensure transform is not null before applying restrictions
  const restrictedTransform = transform
    ? {
        ...transform,
        y: Math.min(transform.y, 0), // Restrict Y-axis movement
      }
    : { x: 0, y: 0 }; // Default values when transform is null

  const style = {
    transform: transform
      ? CSS.Transform.toString({ ...restrictedTransform, x: transform.x }) // Keep X-axis behavior unchanged
      : undefined,
    transition,
    width: "98px",
    height: "130px",
    marginTop: "30px",
    cursor: "pointer",
    flexShrink: 0,
    marginRight: "16px",
  };
  return (
    <MDBox
      ref={setNodeRef}
      style={style}
      sx={{
        position: "relative",
      }}
      {...attributes}
      {...listeners}
    >
      <img
        src={preview}
        alt={`Preview ${index + 1}`}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
        }}
        onClick={() => {
          openLightbox(index);
        }}
      />
      <CloseIcon
        data-no-dnd="true"
        onClick={(e) => {
          // Prevent drag events from interfering
          e.stopPropagation();
          handleRemoveFile(index);
        }}
        fontSize="small"
        color="black"
        sx={{
          position: "absolute",
          top: 1,
          right: 1,
          zIndex: 10,
          background: "white",
          borderRadius: "50%",
        }}
      />
    </MDBox>
  );
};

const PreviewsDraggableArea = ({
  previews,
  handleRemoveFile,
  pdfLoading,
  openLightbox,
  setPreviews,
  setFiles,
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 10, // Require a 10px movement to start dragging
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setPreviews((currentPreviews) => {
        const oldIndex = active.id;
        const newIndex = over.id;
        return arrayMove(currentPreviews, oldIndex, newIndex);
      });

      setFiles((currentFiles) => {
        const oldIndex = active.id;
        const newIndex = over.id;
        return arrayMove(currentFiles, oldIndex, newIndex);
      });
    }
  };

  return (
    <>
      {previews.length > 0 && (
        <MDBox
          display="flex"
          flexDirection="column"
          alignItems="flex-start"
          width="100%"
        >
          <MDTypography variant="h6">Selected Files:</MDTypography>
          <MDTypography variant="button" color="dark">
            Click and Drag image to reorder
          </MDTypography>
        </MDBox>
      )}
      <MDBox width="100%">
        {!pdfLoading ? (
          previews.length > 0 && (
            <MDBox
              sx={{
                marginBottom: "20px",
                overflowY: "hidden",
                overflowX: "auto",
                maxHeight: "13rem",
              }}
            >
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={previews.map((_, index) => index)}
                  strategy={rectSortingStrategy}
                >
                  <MDBox
                    display="flex"
                    alignItems="center"
                    sx={{
                      minWidth: "100%",
                      paddingBottom: "30px",
                      overflowX: "auto",
                      overflowY: "hidden",
                      scrollbarWidth: "thin",
                      scrollbarColor: "#444 #f1f1f1",
                    }}
                  >
                    {previews.map((preview, index) => (
                      <SortablePreviewItem
                        key={index}
                        preview={preview}
                        index={index}
                        handleRemoveFile={handleRemoveFile}
                        openLightbox={openLightbox}
                      />
                    ))}
                  </MDBox>
                </SortableContext>
              </DndContext>
            </MDBox>
          )
        ) : (
          <MDBox
            width="100%"
            height="10rem"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Loader loaderColor="primary" message="Processing Documents..." />
          </MDBox>
        )}
      </MDBox>
    </>
  );
};

export default PreviewsDraggableArea;
