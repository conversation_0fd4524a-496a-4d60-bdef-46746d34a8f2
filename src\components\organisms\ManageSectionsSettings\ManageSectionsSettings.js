import React, { useState, useContext, useEffect } from "react";
import { Card, Grid } from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import MDButton from "components/atoms/MDButton";
import MDInput from "components/atoms/MDInput";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import Loader from "components/atoms/Loader/Loader";
import MDBadgeDot from "components/atoms/MDBadgeDot";

function ManageSectionsSettings() {
  const { apiService } = useContext(ApiServiceContext);
  const [sections, setSections] = useState([]);
  const [newSection, setNewSection] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [isAddingSectionLoading, setIsAddingSectionLoading] = useState(false);

  useEffect(() => {
    fetchSections();
  }, []);

  const fetchSections = async () => {
    try {
      setIsLoading(true);
      const instituteData = await apiService.getInstitute();
      setSections(instituteData.sectionList || []);
    } catch (error) {
      toast.error("Failed to fetch sections");
    } finally {
      setIsLoading(false);
    }
  };

  const validateSection = (section) => {
    const alphanumericRegex = /^[A-Z0-9]+$/;
    if (!alphanumericRegex.test(section)) {
      setError("Only letters and numbers are allowed");
      return false;
    }
    setError("");
    return true;
  };

  const handleAddSection = async () => {
    const sectionToAdd = newSection.trim();

    if (!sectionToAdd) {
      toast.error("Please enter a section name");
      return;
    }

    if (!validateSection(sectionToAdd)) {
      toast.error("Only letters and numbers are allowed");
      return;
    }

    if (sections.includes(sectionToAdd)) {
      toast.error("Section already exists");
      return;
    }

    try {
      setIsAddingSectionLoading(true);
      const updatedSections = [...sections, sectionToAdd];
      await apiService.editInstitute({ availableSections: [sectionToAdd] });
      setSections(updatedSections);
      setNewSection("");
      setError("");
      toast.success("Section added successfully");
    } catch (error) {
      toast.error("Failed to add section");
    } finally {
      setIsAddingSectionLoading(false);
    }
  };

  const handleSectionChange = (e) => {
    const value = e.target.value.toUpperCase();
    setNewSection(value);
    validateSection(value);
  };

  return (
    <Card sx={{ my: { xs: 1, sm: 2 } }}>
      <MDBox p={{ xs: 2, sm: 3, minHeight: "calc(100vh - 13rem)" }}>
        <MDTypography variant="h5" fontWeight="bold">
          Manage Sections
        </MDTypography>
        <MDTypography variant="body2" color="text" mb={3}>
          Add sections for your academic terms
        </MDTypography>

        {isLoading ? (
          <MDBox
            display="flex"
            justifyContent="center"
            alignItems="center"
            sx={{ minHeight: "400px" }}
          >
            <Loader message="Loading sections..." />
          </MDBox>
        ) : (
          <>
            <MDBox mb={3}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <MDInput
                    fullWidth
                    value={newSection}
                    onChange={handleSectionChange}
                    placeholder="Enter Section Name, (eg. A)"
                    error={!!error}
                    helperText={error}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <MDButton
                    variant="gradient"
                    color="info"
                    onClick={handleAddSection}
                    disabled={!!error || isAddingSectionLoading}
                    sx={{ width: "8rem" }}
                  >
                    {isAddingSectionLoading ? (
                      <Loader loaderColor="#fff" />
                    ) : (
                      "Add Section"
                    )}
                  </MDButton>
                </Grid>
              </Grid>
            </MDBox>

            <MDBox>
              <MDTypography variant="h6" fontWeight="medium" mb={1}>
                Available Sections
              </MDTypography>
              <MDBox display="flex" gap={2} flexWrap="wrap">
                {sections?.map((section, index) => (
                  <MDBadgeDot
                    key={index}
                    badgeContent={section}
                    color="info"
                    size="md"
                  />
                ))}
              </MDBox>
            </MDBox>
          </>
        )}
      </MDBox>
    </Card>
  );
}

export default ManageSectionsSettings;

