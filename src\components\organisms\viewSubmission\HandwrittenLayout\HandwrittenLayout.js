import React from "react";
import Latex from "react-latex-next";
import MDTypography from "components/atoms/MDTypography";
import MDBox from "components/atoms/MDBox";

const HandwrittenLayout = ({ content, heading, isInsidePdf }) => {
  return (
    <>
      {/* Heading */}
      <MDTypography variant="h6" ml={1}>
        {heading}
      </MDTypography>
      <MDBox
        sx={{
          width: "100%",
          margin: "auto",
          mt: 1,
          padding: isInsidePdf ? "0rem 2rem" : "0.7rem 2rem",
          paddingBottom: "1rem",
          backgroundColor: isInsidePdf ? "#fff" : "#fafafa",
          borderRadius: 1,
          boxShadow: 1,
          border: !isInsidePdf && "1px solid #ddd",
          backgroundImage:
            !isInsidePdf &&
            "repeating-linear-gradient(white, white 20px, #797979 21px)",
          backgroundSize: !isInsidePdf && "100% 32px",
          backgroundRepeat: !isInsidePdf && "repeat-y",
          position: "relative",
          display: "inline-block",
        }}
      >
        {isInsidePdf ? (
          <MDTypography
            variant="body1"
            sx={{
              paddingTop: 1,
              fontSize: "1.2rem",
              color: "#284283",
              lineHeight: 1.8,
            }}
          >
            <Latex>{content}</Latex>
          </MDTypography>
        ) : (
          <MDTypography
            variant="body1"
            sx={{
              paddingTop: 1,
              fontSize: {
                xs: "0.8rem",
                sm: "1rem",
              },
              color: heading === "Feedback" ? "#df0000" : "#284283",
              lineHeight: 1.8,
            }}
          >
            <Latex>{content}</Latex>
          </MDTypography>
        )}
      </MDBox>
    </>
  );
};

export default HandwrittenLayout;
