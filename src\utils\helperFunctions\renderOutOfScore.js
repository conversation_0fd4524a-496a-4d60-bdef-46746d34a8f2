import MDTypography from "components/atoms/MDTypography";

const renderOutOfScore = (achievedScore, totalScore, isBig = false) => {
  if (totalScore === achievedScore) {
    return (
      <MDTypography variant={isBig ? "h4" : "h6"} color="success">
        {`${achievedScore} / ${totalScore}`}
      </MDTypography>
    );
  } else if (achievedScore === 0) {
    return (
      <MDTypography variant={isBig ? "h4" : "h6"} color="error">
        {`${achievedScore} / ${totalScore}`}
      </MDTypography>
    );
  } else if (achievedScore < totalScore) {
    return (
      <MDTypography variant={isBig ? "h4" : "h6"} color="warning">
        {`${achievedScore} / ${totalScore}`}
      </MDTypography>
    );
  } else if (!achievedScore) {
    return (
      <MDTypography variant={isBig ? "h4" : "h6"} color="error">
        {`0 / ${totalScore}`}
      </MDTypography>
    );
  } else {
    return (
      <MDTypography variant={isBig ? "h4" : "h6"} color="info">
        NA
      </MDTypography>
    );
  }
};

export default renderOutOfScore;
