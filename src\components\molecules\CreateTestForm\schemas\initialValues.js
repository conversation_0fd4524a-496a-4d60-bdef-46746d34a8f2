import checkout from "./form";

const {
  formField: {
    name,
    totalScore,
    class: classField,
    subjectName,
    duration,
    sectionList,
    questions
  },
} = checkout;

const initialValues = {
  [name.name]: "",
  [totalScore.name]: "",
  [classField.name]: "",
  [subjectName.name]: "",
  [duration.name]: 0,
  [sectionList.name]: [],
  [questions.name]: [
    {
      questionNumber: 0,
      question: "",
      questionScore: 0,
      questionRubric: "",
      // topics: [
      //   {
      //     chapter: [],
      //     topics: [],
      //   }
      // ]
    },
  ],
};

export default initialValues;
