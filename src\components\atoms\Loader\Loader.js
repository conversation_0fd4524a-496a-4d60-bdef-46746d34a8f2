import * as React from 'react';
import CircularProgress from '@mui/material/CircularProgress';
import MDBox from 'components/atoms/MDBox';
import clsx from "clsx";
import MDTypography from '../MDTypography';

export default function Loader({ fullScreen, message, loaderColor="", overlay }) {
  const LoaderWrapperClasses = clsx({
    'flex justify-center items-center h-screen w-screen gap-3': fullScreen,
    'flex justify-center items-center gap-3': !fullScreen,
    '!gap-0': !message, 
  });

  const LoaderClasses = clsx({
    '!h-[20px] !w-[20px]': !fullScreen,
  });

  return (
  overlay ?(
    <div className="fixed left-0 top-0 flex h-screen w-screen items-center justify-center bg-[#00000045]" style={{zIndex: '1000'}}>
    <MDBox className={LoaderWrapperClasses}>
      <CircularProgress
        className={LoaderClasses}
        sx={loaderColor && loaderColor.startsWith("#") ? { color: loaderColor } : ''}
        color={loaderColor && !loaderColor.startsWith("#") ? loaderColor : "inherit"}
      />
      <MDTypography variant="h5" fontWeight="medium">{message}</MDTypography>
    </MDBox>
    </div>
  ): (
    <MDBox className={LoaderWrapperClasses}>
    <CircularProgress
      className={LoaderClasses}
      sx={loaderColor && loaderColor.startsWith("#") ? { color: loaderColor } : ''}
      color={loaderColor && !loaderColor.startsWith("#") ? loaderColor : "inherit"}
    />
    <MDTypography variant="h5" fontWeight="medium">{message}</MDTypography>
  </MDBox>
  )
  );
}
