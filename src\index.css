@tailwind base;
@tailwind components;
@tailwind utilities;

/* Disable the spinner and scroll behavior for number inputs */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type="number"]:hover {
  pointer-events: none;
}

input[type="number"]:focus {
  pointer-events: auto;
}

.no-wrap {
  white-space: nowrap;
}

.modal__dropdown .MuiOutlinedInput-root {
  padding: 5px !important;
}

.assignment_dropdown .MuiInputBase-formControl {
  padding-bottom: 2px !important;
}

.highcharts-axis-labels text {
  fill: black !important;
  font-weight: normal !important;
  text-decoration: none !important;
  font-size: 14px;
}
.clickable-label {
  color: black !important;
  font-weight: normal !important;
  text-decoration: none !important;
  font-size: 14px;
  cursor: pointer; /* Indicates that the label is clickable */
}

.highcharts-axis-labels {
  text-align: center;
}
.highcharts-axis-labels span {
  text-decoration: none !important;
}

.highcharts-axis-labels .chapter-label {
  color: black !important;
  font-weight: normal !important;
  text-decoration: none !important;
  font-size: 14px;
  text-decoration: none !important;
}

.student-clickable-label {
  color: white !important;
  font-weight: normal !important;
  text-decoration: none !important;
  font-size: 14px;
  cursor: pointer; /* Indicates that the label is clickable */
}

.highcharts-axis-labels .student-chapter-label {
  color: white !important;
  font-weight: normal !important;
  text-decoration: none !important;
  font-size: 14px;
  text-decoration: none !important;
}

.submission-bar-label {
  font-size: "12px";
  font-weight: 600;
}

/* Target the MUI Accordion transition duration */
.MuiAccordion-root {
  transition-duration: 0.3s !important; /* Adjust the timing as needed */
}

.MuiAccordionSummary-root {
  transition: background-color 0.3s ease-in-out !important; /* Adjust timing for the Summary background color */
}

.MuiCollapse-root {
  transition-duration: 0.3s !important; /* Faster expand/collapse */
}

.MuiAutocomplete-listbox {
  scrollbar-color: #888888 #f0f0f0;
}