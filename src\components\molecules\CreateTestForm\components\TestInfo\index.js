import React, { useEffect, useState, useContext } from "react";
import PropTypes from "prop-types";
import Grid from "@mui/material/Grid";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import FormField from "components/atoms/FormField";
import { Autocomplete, TextField } from "@mui/material";
import { ApiServiceContext } from "context";
import Popper from "@mui/material/Popper";

export function TestInfo({ formData, viewOnly, formType }) {
  const [open, setOpen] = useState(false);
  const classOptions = [6, 7, 8, 9, 10, 11, 12];
  const { apiService } = useContext(ApiServiceContext);
  const { formField, values, errors, touched, setFieldValue } = formData;
  const {
    name,
    totalScore,
    class: classField,
    subjectName,
    sectionList,
  } = formField;
  const {
    name: testName,
    totalScore: totalScoreV,
    class: gradeNumber,
    subjectName: testSubject,
    sectionList: selectedSections,
  } = values;
  const [subjects, setSubjects] = useState([]); // State for dynamic subject list

  const defaultSections = ["A", "B", "C", "D"];
  const [sectionListOptions, setSectionListOptions] = useState([]);

  // Function to fetch subjects based on class number
  const getSubjectsByGrade = async (gradeNumber) => {
    try {
      const res = await apiService.getSubjectsByGrade(gradeNumber);
      setSubjects(res || []); // Set fetched subjects
    } catch (error) {
      setSubjects([]); // Clear subjects on error
    }
  };

  // Fetch subjects when the class field changes, ensuring gradeNumber is a number
  useEffect(() => {
    if (formType === "view") return;
    if (gradeNumber && !isNaN(gradeNumber)) {
      getSubjectsByGrade(gradeNumber);
    } else {
      setSubjects([]); // Clear subjects if no valid class is selected
    }
  }, [gradeNumber, formType]);

  const getSectionListApi = async () => {
    try {
      const res = await apiService.getInstitute();

      if (Array.isArray(res.sectionList) && res.sectionList.length > 0) {
        setSectionListOptions(res.sectionList);
      } else {
        setSectionListOptions(defaultSections);
      }
    } catch (error) {
      setSectionListOptions(defaultSections);
    }
  };

  useEffect(() => {
    getSectionListApi();
  }, []);

  return (
    <MDBox px={3} py={1}>
      <MDBox lineHeight={0}>
        <MDTypography variant="h5">
          {formType === "view"
            ? "View Assignment"
            : formType === "add"
            ? "Create Assignment"
            : "Edit Assignment"}
        </MDTypography>
        <MDTypography variant="button" color="text">
          Mandatory informations
        </MDTypography>
      </MDBox>
      <MDBox mt={1.625}>
        <Grid container spacing={3}>
          <Grid
            item
            xs={12}
            sm={6}
            sx={{
              position: "relative",
              height: 100,
            }}
          >
            <FormField
              type={name.type}
              label={name.label}
              name={name.name}
              value={testName}
              placeholder={name.placeholder}
              error={errors.name && touched.name}
              success={testName.length > 0 && !errors.name}
              disabled={viewOnly}
            />
            {testName.length > 0 && (
              <MDTypography
                variant="caption"
                color={
                  testName.length < 5
                    ? "error"
                    : testName.length > 100
                    ? "error"
                    : "success"
                }
                fontWeight="medium"
                sx={{
                  position: "absolute",
                  right: 5,
                  bottom: 10,
                }}
              >
                {`${testName.length}/100`}
              </MDTypography>
            )}
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormField
              type={totalScore.type}
              label={totalScore.label}
              name={totalScore.name}
              value={totalScoreV}
              placeholder={totalScore.placeholder}
              error={errors.totalScore && touched.totalScore}
              success={String(totalScoreV).length > 0 && !errors.totalScore}
              disabled={viewOnly}
            />
          </Grid>
        </Grid>
        <Grid container spacing={3} sx={{ marginTop: "1px" }}>
          <Grid item xs={12} sm={4}>
            <Autocomplete
              autoSelect
              autoHighlight
              options={classOptions}
              gradeNumber="assignment_dropdown"
              getOptionLabel={(option) => option.toString()}
              value={gradeNumber || ""}
              onChange={(event, newValue) => {
                setSubjects([]);
                const selectedClass = newValue;
                setFieldValue(classField.name, selectedClass);
                setFieldValue(subjectName.name, "");
              }}
              disabled={viewOnly}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  label={"Grade"}
                  placeholder="Select Grade"
                  error={errors.classes && touched.classes}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <Autocomplete
              autoSelect
              autoHighlight
              multiple
              options={sectionListOptions}
              getOptionLabel={(option) => option}
              value={selectedSections || []}
              onChange={(event, newValue) => {
                setFieldValue(sectionList.name, newValue);
              }}
              gradeNumber="assignment_dropdown"
              disabled={viewOnly}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  label={sectionList.label}
                  placeholder="Select sections"
                  error={errors.sectionList && touched.sectionList}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Autocomplete
              autoSelect
              autoHighlight
              options={
                subjects.length > 0
                  ? subjects
                  : ["select class to show subjects"]
              }
              getOptionLabel={(option) => option}
              value={testSubject || ""}
              onChange={(event, newValue) => {
                setFieldValue(subjectName.name, newValue);
              }}
              disabled={viewOnly}
              open={open}
              onOpen={() => setOpen(true)}
              onClose={() => setOpen(false)}
              gradeNumber="assignment_dropdown"
              freeSolo={false}
              disableClearable
              PopperComponent={(props) => <Popper {...props} open={open} />}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  label={subjectName.label}
                  placeholder={"Select Subject"}
                  error={false}
                />
              )}
              renderOption={(props, option) => {
                const isDisabled = subjects.length === 0;
                return (
                  <li
                    {...props}
                    style={{
                      pointerEvents: isDisabled ? "none" : "auto",
                      color: "gray",
                    }}
                    onMouseEnter={(e) => {
                      if (!isDisabled) {
                        e.target.style.color = "#515151";
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isDisabled) {
                        e.target.style.color = "";
                      }
                    }}
                  >
                    {option}
                  </li>
                );
              }}
            />
          </Grid>
        </Grid>
      </MDBox>
    </MDBox>
  );
}

TestInfo.propTypes = {
  formData: PropTypes.oneOfType([PropTypes.object, PropTypes.func]).isRequired,
};
