import { Card, Skeleton } from "@mui/material";
import MDBox from "components/atoms/MDBox";

const StatisticsCardSkeleton = () => (
  <Card>
    <MDBox
      display="flex"
      flexDirection="column"
      alignItems="flex-end"
      justifyContent="space-between"
      pr={2}
      py={2}
      position="relative"
      height={128}
    >
      <MDBox
        bgColor="light"
        color="light"
        coloredShadow="light"
        borderRadius="xl"
        width="4rem"
        height="4rem"
        zIndex={1}
        sx={{
          position: "absolute",
          top: "-1rem",
          left: "1.3rem",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Skeleton animation="wave" width="60%" height="100%" />
      </MDBox>
      <Skeleton animation="wave" width={100} />
      <Skeleton animation="wave" width={100} height={50} />
    </MDBox>
  </Card>
);

export default StatisticsCardSkeleton;

