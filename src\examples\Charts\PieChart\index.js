/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

import { useEffect, useMemo } from "react";

// porp-types is a library for typechecking of props
import PropTypes from "prop-types";

// react-chartjs-2 components
import { Pie } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";

// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";

// PieChart configurations
import configs from "examples/Charts/PieChart/configs";
// import { Box } from "@mui/material";

ChartJS.register(ArcElement, Tooltip, Legend);

function PieChart({
  icon,
  title,
  description,
  height,
  chart,
  onRenderComplete,
}) {
  const { data, options } = configs(chart.labels || [], chart.datasets || {});

  // const backgroundColors = data.datasets?.[0]?.backgroundColor || [];
  // const chartData = data.datasets?.[0]?.data || [];

  // const renderDetails = () => (
  //   <MDBox display="flex" alignItems="center" width="100%" flexWrap="wrap">
  //     {chart.labels.map((label, index) => (
  //       <MDBox key={label} display="flex" alignItems="center" mt={1} mr={2} >
  //         <Box
  //           width="1rem"
  //           height="1rem"
  //           bgcolor={backgroundColors[index]}
  //           mr={1}
  //         />
  //         <MDTypography variant="button" color="text" fontWeight="medium">
  //           {label}: {chartData[index]}
  //         </MDTypography>
  //       </MDBox>
  //     ))}
  //   </MDBox>
  // );

  useEffect(() => {
    if (onRenderComplete) {
      onRenderComplete();
    }
  }, [onRenderComplete, chart]);

  const renderChart = (
    <MDBox py={2} pr={2} pl={icon.component ? 1 : 2}>
      {title || description ? (
        <MDBox display="flex" px={description ? 1 : 0} pt={description ? 1 : 0}>
          {icon.component && (
            <MDBox
              width="4rem"
              height="4rem"
              bgColor={icon.color || "dark"}
              variant="gradient"
              coloredShadow={icon.color || "dark"}
              borderRadius="xl"
              display="flex"
              justifyContent="center"
              alignItems="center"
              color="white"
              mt={-5}
              mr={2}
            >
              <Icon fontSize="medium">{icon.component}</Icon>
            </MDBox>
          )}
          <MDBox mt={icon.component ? -2 : 0}>
            {title && <MDTypography variant="h6">{title}</MDTypography>}
            <MDBox mb={2}>
              <MDTypography component="div" variant="button" color="text">
                {description}
              </MDTypography>
            </MDBox>
          </MDBox>
        </MDBox>
      ) : null}
      {useMemo(
        () => (
          <MDBox height={height}>
            <Pie data={data} options={options} />
          </MDBox>
        ),
        [chart, height]
      )}
      {/* {chart.labels && chart.datasets && (
        <MDBox
          mt={2}
          sx={{
            display: "flex",
            justifyContent: "center",
            gap: "30px",
            width: "100%",
          }}
        >
          {renderDetails()}
        </MDBox>
      )} */}
    </MDBox>
  );

  return title || description ? <Card>{renderChart}</Card> : renderChart;
}

// Setting default values for the props of PieChart
PieChart.defaultProps = {
  icon: { color: "info", component: "" },
  title: "",
  description: "",
  height: "14.125rem",
};

// Typechecking props for the PieChart
PieChart.propTypes = {
  icon: PropTypes.shape({
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "light",
      "dark",
    ]),
    component: PropTypes.node,
  }),
  title: PropTypes.string,
  description: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  chart: PropTypes.objectOf(
    PropTypes.oneOfType([PropTypes.array, PropTypes.object])
  ).isRequired,
};

export default PieChart;
