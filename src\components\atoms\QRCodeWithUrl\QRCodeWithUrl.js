import React, { useState } from "react";
import { Box, IconButton, TextField, Tooltip } from "@mui/material";
import { QRCodeCanvas } from "qrcode.react";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import { toast } from "react-toastify";
import MDTypography from "../MDTypography";

const QRCodeWithUrl = ({ phoneUrl }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(phoneUrl);
    setCopied(true);
    toast.success("URL copied to clipboard");
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Box className="flex justify-center items-center my-4">
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          p: 2,
          width: "100%",
          gap: 3,
          maxWidth: "750px",
        }}
      >
        {/* QR Code */}
        <QRCodeCanvas value={phoneUrl} size={128} />

        <MDTypography variant="h5" fontWeight="medium" className="text-center">
          Or Visit
        </MDTypography>

        {/* URL Box and Copy Button */}
        <Box sx={{ display: "flex", width: "100%" }}>
          <TextField
            variant="outlined"
            value={phoneUrl}
            fullWidth
            InputProps={{
              readOnly: true,
              style: { fontSize: "14px", padding: "5px" },
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: "8px",
                height: "40px",
                fontSize: "14px",
                backgroundColor: "#f9f9f9",
              },
            }}
          />
          <Tooltip title={copied ? "Copied!" : "Copy to clipboard"}>
            <IconButton
              disabled={copied}
              onClick={handleCopy}
              sx={{ ml: 1, p: 1 }}
            >
              <ContentCopyIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    </Box>
  );
};

export default QRCodeWithUrl;

