import React, { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { Card } from "../Card/Card";
import CloseIcon from "@mui/icons-material/Close";
import { useMaterialUIController } from "context";

export function Modal({
  children,
  fullWidth,
  isOpen,
  onClose,
  headingText,
  closeIconVisible = true,
}) {
  const modalRef = useRef(null);
  const [controller, dispatch] = useMaterialUIController();
  const { layout } = controller;
  const [fullWidthState, setFullWidth] = useState(fullWidth);

  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === "Escape" && closeIconVisible !== false) {
        onClose();
      }
    };

    // const handleClickOutside = (event) => {
    //   if (modalRef.current && !modalRef.current.contains(event.target)) {
    //     onClose();
    //   }
    // };

    document.addEventListener("keydown", handleEscape);
    // document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("keydown", handleEscape);
      // document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1200) {
        setFullWidth(true);
      } else {
        setFullWidth(false);
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial check

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  if (!isOpen) {
    return null;
  }

  return createPortal(
    <div
      className="fixed left-0 top-0 flex h-screen w-screen items-center justify-center bg-[#00000045]"
      style={{ zIndex: "999" }}
    >
      <Card
        ref={modalRef}
        className={`relative rounded-xl p-6 ${
          fullWidthState ? "w-full xs:ml-0 lg:ml-36 xl:ml-0" : "w-5/12"
        } max-h-[90vh] sm:max-h-[80vh] overflow-auto m-5`}
      >
        <header className="flex w-full cursor-pointer items-center justify-between">
          <h3 className="text-lg font-bold">{headingText}</h3>
          {closeIconVisible && <CloseIcon onClick={onClose} />}
        </header>
        <div className="overflow-auto">{children}</div>
      </Card>
    </div>,
    document?.body
  );
}
