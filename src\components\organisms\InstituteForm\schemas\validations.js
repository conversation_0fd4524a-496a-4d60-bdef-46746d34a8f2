import * as Yup from "yup";
import form from "./form";

const {
  formField: { name, program, addressOne, addressTwo, city, state, pincode },
} = form;

const validations = [
  Yup.object().shape({
    [name.name]: Yup.string()
      .max(50, "Institute Name must be at most 50 characters")
      .required(name.errorMsg),
    [program.name]: Yup.string().max(
      50,
      "Program name must be at most 50 characters"
    ),
    // .required(program.errorMsg),
    [addressOne.name]: Yup.string()
      .max(45, "Address Line 1 must be at most 45 characters")
      .required(addressOne.errorMsg),
    [addressTwo.name]: Yup.string().max(
      45,
      "Address Line 2 must be at most 45 characters"
    ),
    [city.name]: Yup.string()
      .max(30, "City must be at most 30 characters")
      .required(city.errorMsg),
    [state.name]: Yup.string()
      .max(30, "State must be at most 30 characters")
      .required(state.errorMsg),
    [pincode.name]: Yup.string()
      .matches(/^\d+$/, "Pincode must be a number")
      .max(10, "Pincode must be at most 10 digits")
      .min(5, "Pincode must be at least 5 digits")
      .required(pincode.errorMsg),
  }),
];

export default validations;
