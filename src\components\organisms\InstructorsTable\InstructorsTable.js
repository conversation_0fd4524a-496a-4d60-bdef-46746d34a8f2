import React, { useState, useEffect, useContext } from "react";
import Card from "@mui/material/Card";
import MDBox from "components/atoms/MDBox";
import DataTable from "examples/Tables/DataTable";
import { Modal } from "components/atoms/Modal/Modal";
import { toast } from "react-toastify";
import Loader from "components/atoms/Loader/Loader";
import { ApiServiceContext } from "context";
import { InstructorsTableHeader } from "constants/InstructorsTableHeader/InstructorsTableHeader";
import { TableInfo } from "components/molecules/TableInfo/TableInfo";
import { DeleteModal } from "components/molecules/DeleteModal/DeleteModal";
import { authenticateUser } from "utils/helperFunctions/authenticateUser";
import { InstructorForm } from "../InstructorForm/InstructorForm";

function InstructorsTable() {
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);
  const authUser = authenticateUser(apiService, loading, handleLogin);
  useEffect(() => {
    authUser();
  }, [authUser]);

  const [allInstructors, setAllInstructors] = useState([]);
  const [instructorEmail, setInstructorEmail] = useState(""); // Change from instructorId to instructorEmail
  const [isLoading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tableData, setTableData] = useState({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [instructorFormData, setInstructorFormData] = useState({});
  const [formType, setFormType] = useState("edit");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [submitLoader, setSubmitLoader] = useState(false);
  const [deleteInstructorLoader, setDeleteInstructorLoader] = useState(false);

  const handleEditInstructorModal = (data) => {
    setIsModalOpen(true);
    setFormType("edit");
    setInstructorFormData(data?.row?.original);
  };

  const handleAddInstituteModal = () => {
    setIsModalOpen(true);
    setFormType("add");
  };

  const handleDeleteInstructor = async () => {
    setDeleteInstructorLoader(true);
    try {
      await apiService.deleteInstructor(instructorEmail); // Pass email instead of instructorId
      await fetchInstructors(); // Refresh the instructor list
      toast.success("Instructor deleted successfully");
    } catch (err) {
      setError(err);
      toast.error(err.message || "Error deleting instructor");
    } finally {
      setDeleteInstructorLoader(false);
      setIsDeleteModalOpen(false);
    }
  };

  const handleDeleteInstructorModal = (instructorData) => {
    setIsDeleteModalOpen(true);
    setInstructorEmail(instructorData?.row?.original?.email); // Store email instead of instructorId
  };

  const fetchInstructors = async () => {
    try {
      const result = await apiService.getInstructorsDetails();
      setAllInstructors(result);
      setLoading(false);
    } catch (err) {
      setError(err);
      setLoading(false);
      toast.error("Error in fetching instructor details");
    }
  };

  useEffect(() => {
    fetchInstructors();
  }, [apiService]);

  useEffect(() => {
    setTableData({
      columns: InstructorsTableHeader,
      rows: allInstructors,
    });
  }, [allInstructors]);

  const handleFormSubmit = async (value) => {
    setSubmitLoader(true);
    try {
      if (formType === "edit") {
        const instructorData = {
          instructorId: value.instructorId,
          email: value.email,
          firstName: value.firstName,
          lastName: value.lastName,
          role: value.role || 1,
        };

        await apiService.editInstructor(instructorData);
        await fetchInstructors(); // Refresh the instructor list
        toast.success("Instructor edited successfully");
      } else if (formType === "add") {
        const instructorData = {
          email: value.email,
          firstName: value.firstName,
          lastName: value.lastName,
          role: value.role || 1,
        };

        await apiService.createNewInstructor(instructorData);
        await fetchInstructors(); // Refresh the instructor list
        toast.success("Instructor added successfully");
      }
      setIsModalOpen(false);
    } catch (err) {
      toast.error(err.message || "Error processing instructor data");
    } finally {
      setSubmitLoader(false);
    }
  };

  return (
    <>
      <DeleteModal
        isModalOpen={isDeleteModalOpen}
        setIsModalOpen={setIsDeleteModalOpen}
        handleChange={handleDeleteInstructor}
        title="Delete Instructor"
        loader={deleteInstructorLoader}
      />
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        headingText={
          formType === "add" ? "Add new instructor" : "Edit Instructor"
        }
      >
        <InstructorForm
          formData={formType === "edit" ? instructorFormData : ""}
          handleSubmit={handleFormSubmit}
          loader={submitLoader}
        />
      </Modal>
      <MDBox py={1} mb={3}>
        {isLoading ? (
          <MDBox
            width="100%"
            height="450px"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Loader message="Loading Instructors Data..." />
          </MDBox>
        ) : (
          <>
            <TableInfo
              tableTitle="Instructors"
              tableDesc="List of all the instructors"
              buttonText="Add New Instructor"
              handleClick={handleAddInstituteModal}
            />
            <DataTable
              table={tableData}
              editData
              deleteData
              onEdit={handleEditInstructorModal}
              onDelete={handleDeleteInstructorModal}
            />
          </>
        )}
      </MDBox>
    </>
  );
}

export default InstructorsTable;
