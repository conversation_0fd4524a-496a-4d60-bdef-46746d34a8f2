import { Icon, MenuItem, Select, TextField, Checkbox, ListItemText } from "@mui/material";
import { useMemo } from "react";

export function ColumnWiseFilter({
  column: { filterValue = [], setFilter, filterType, preFilteredRows, id },
}) {
  const uniqueValues = useMemo(() => {
    const options = new Set();
    preFilteredRows.forEach((row) => {
      let cellValue = row.values[id];
      if (cellValue == null) cellValue = '0';
      if (Array.isArray(cellValue)) {
        cellValue.forEach(value => {
          options.add(value == null ? '0' : value);
        });
      } else {
        options.add(cellValue);
      }
    });
    return [...options].sort((a, b) => {
      if (a < b) return -1;
      if (a > b) return 1;
      return 0;
    });
  }, [id, preFilteredRows]);

  const handleChange = (event) => {
    const { value } = event.target;
    if (value.includes("All")) {
      setFilter([]);
    } else {
      setFilter(value);
    }
  };

  return (
    <>
      {(filterType === "dropdown" || filterType === "search") && (
        filterType === "dropdown" ? (
          <Select
            multiple
            value={filterValue}
            onChange={handleChange}
            displayEmpty
            fullWidth
            renderValue={(selected) => selected.length === 0 ? "All" : selected.join(', ')}
            sx={{ padding: "10px", fontSize: "12px", marginBottom: '15px' }}
            IconComponent={() => <Icon>arrow_drop_down</Icon>}
          >
            <MenuItem value="All">
              <Checkbox checked={filterValue.length === 0} />
              <ListItemText primary="All" />
            </MenuItem>
            {uniqueValues.map((value, idx) => (
              <MenuItem key={idx} value={value}>
                <Checkbox checked={filterValue.indexOf(value) > -1} />
                <ListItemText primary={value} sx={{ textTransform: 'capitalize' }} />
              </MenuItem>
            ))}
          </Select>
        ) : (
          <TextField
            label="Search..."
            variant="outlined"
            value={filterValue || ""}
            onChange={(e) => setFilter(e.target.value || undefined)}
            sx={{
              "& .MuiInputLabel-root": {
                fontSize: "12px",
              },
              "& .MuiOutlinedInput-input": {
                fontSize: "12px",
                padding: '10px',
              },
              marginBottom: '15px',
            }}
          />
        )
      )}
    </>
  );
}

export function DefaultColumnFilter(rows, id, filterValue) {
  return rows.filter(row => {
    const cellValue = row.values[id];

    if (Array.isArray(filterValue)) {
      if (filterValue.length === 0) return true;

      return filterValue.some(value => {
        if (value === '0') {
          return cellValue == null;
        } else {
          return String(cellValue) === String(value);
        }
      });
    } else {
      if (!filterValue) return true;
      if (filterValue === '0') {
        return cellValue == null;
      } else {
        return String(cellValue).toLowerCase().includes(String(filterValue).toLowerCase());
      }
    }
  });
}
