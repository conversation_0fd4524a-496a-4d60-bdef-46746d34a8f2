import React, { useContext, useEffect, useState } from "react";
import { Grid } from "@mui/material";
import MDBox from "components/atoms/MDBox";
import { ApiServiceContext } from "context";
import ComplexStatisticsCard from "examples/Cards/StatisticsCards/ComplexStatisticsCard";
import { toast } from "react-toastify";
import StatisticsCardSkeleton from "../../SkeletonLoaders/StatisticsCardSkeleton";

const OverallStatsCards = () => {
  const { apiService } = useContext(ApiServiceContext);

  const [overallStats, setOverallStats] = useState(null);

  useEffect(() => {
    if (overallStats) return;
    apiService
      .getInstituteOverallStats()
      .then((data) => setOverallStats(data))
      .catch(() => {
        return toast.error("Error loading institute stats");
      });
  }, []);

  return (
    <MDBox mt={5}>
      <Grid
        container
        spacing={{
          xs: 5,
          lg: 3,
        }}
      >
        <Grid item xs={12} sm={6} lg={3}>
          {overallStats ? (
            <ComplexStatisticsCard
              color="dark"
              icon="school"
              title="Students"
              count={overallStats?.students || 0}
              sx={{ height: "8rem" }}
            />
          ) : (
            <StatisticsCardSkeleton />
          )}
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          {overallStats ? (
            <ComplexStatisticsCard
              icon="person"
              title="Instructors"
              count={overallStats?.instructors || 0}
              color="info"
              sx={{ height: "8rem" }}
            />
          ) : (
            <StatisticsCardSkeleton />
          )}
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          {overallStats ? (
            <ComplexStatisticsCard
              color="success"
              icon="book"
              title="Assignments"
              count={overallStats?.assessments || 0}
              sx={{ height: "8rem" }}
            />
          ) : (
            <StatisticsCardSkeleton />
          )}
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          {overallStats ? (
            <ComplexStatisticsCard
              color="error"
              icon="leaderboard"
              title="Avg. Student Performance"
              count={
                overallStats?.overallStudentPerformance
                  ? `${overallStats?.overallStudentPerformance || 0}%`
                  : "NA"
              }
              sx={{ height: "8rem" }}
            />
          ) : (
            <StatisticsCardSkeleton />
          )}
        </Grid>
      </Grid>
    </MDBox>
  );
};

export default OverallStatsCards;
