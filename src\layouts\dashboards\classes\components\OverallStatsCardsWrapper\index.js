import React, { useContext, useEffect, useState } from "react";
import Grid from "@mui/material/Grid";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import MiniStatisticsCard from "examples/Cards/StatisticsCards/MiniStatisticsCard";
import StatsCardsSkeleton from "../Loaders/StatsCardsSkeleton";

const OverallStatsCardsWrapper = ({ grade }) => {
  const { apiService } = useContext(ApiServiceContext);
  const [overallStats, setOverallStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchOverallStats = async () => {
      try {
        setLoading(true);
        const stats = await apiService.getClassOverallStats(grade);
        setOverallStats(stats);
      } catch (err) {
        toast.error("Error loading overall stats");
      } finally {
        setLoading(false);
      }
    };

    fetchOverallStats();
  }, [apiService, grade]);

  if (loading) return <StatsCardsSkeleton />;

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={4}>
        <MiniStatisticsCard
          title={{ text: "Total Students" }}
          count={overallStats?.students || 0}
          icon={{ color: "info", component: "school" }}
        />
      </Grid>
      <Grid item xs={12} sm={4}>
        <MiniStatisticsCard
          title={{ text: "Total Assignments" }}
          count={overallStats?.assessments || 0}
          icon={{ color: "info", component: "article" }}
        />
      </Grid>
      <Grid item xs={12} sm={4}>
        <MiniStatisticsCard
          title={{ text: "Overall Grade Performance" }}
          count={`${overallStats?.overallStudentPerformance || 0}%`}
          icon={{ color: "info", component: "person" }}
        />
      </Grid>
    </Grid>
  );
};

export default OverallStatsCardsWrapper;
