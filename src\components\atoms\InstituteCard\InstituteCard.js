import React, { useContext, useState } from "react";
import { Grid } from "@mui/material";
import MDTypography from "components/atoms/MDTypography";
import MDBox from "components/atoms/MDBox";
import { useNavigate } from "react-router-dom";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import checkIsCurrentTerm from "utils/helperFunctions/checkIsCurrentTerm";
import Loader from "components/atoms/Loader/Loader";

function InstituteCard({ data }) {
  const navigate = useNavigate();
  const { apiService } = useContext(ApiServiceContext);
  const { city, state } = data.address;
  const [isLoading, setIsLoading] = useState(false);

  const handleSelectInstitute = async () => {
    try {
      setIsLoading(true);

      if (!data.id || !data.role) {
        throw new Error("Invalid institute data");
      }

      // Set institute ID first
      apiService.setInstituteId(data.id);

      // Set role
      apiService.setUserRole(data.role);

      await new Promise((resolve) => setTimeout(resolve, 200));

      // Fetch terms for the selected institute
      const terms = await apiService.getTerms();
      if (!terms) {
        throw new Error("Failed to fetch terms");
      }

      // Check if there's a current term
      const hasCurrentTerm = terms.some((term) =>
        checkIsCurrentTerm(term.startDate, term.endDate)
      );

      const redirect_url = hasCurrentTerm ? "/" : "/settings/advanced";

      // Navigate based on current term status
      navigate(redirect_url, { replace: true });
    } catch (error) {
      toast.error("Failed to select institute");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Grid item xs={12}>
      <MDBox
        sx={{
          px: 3,
          py: 2,
          cursor: "pointer",
          display: "flex",
          justifyContent: "flex-start",
          alignItems: "center",
          boxShadow: 2,
          borderRadius: 3,
          border: "1px solid #c9c9c9",
          background: "linear-gradient(135deg, #f9f9f9, #ffffff)",
          transition: "all 0.3s ease-in-out",
          "&:hover": {
            boxShadow: 4,
            transform: "scale(1.01)",
          },
          position: "relative", // Added for loader positioning
        }}
        onClick={handleSelectInstitute}
      >
        {isLoading ? (
          <MDBox
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              zIndex: 1,
              height: "2.8rem",
              width: "100%",
            }}
          >
            <Loader message="Switching Institute..." />
          </MDBox>
        ) : (
          <MDBox>
            <MDTypography variant="h6" fontWeight="bold">
              {data.name}
            </MDTypography>
            <MDTypography variant="h6" color="text">
              {`${city}, ${state}`}
            </MDTypography>
          </MDBox>
        )}
      </MDBox>
    </Grid>
  );
}

export default InstituteCard;
