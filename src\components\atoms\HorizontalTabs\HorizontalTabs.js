import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import Grid from "@mui/material/Grid";
import AppBar from "@mui/material/AppBar";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import MDBox from "components/atoms/MDBox";
import breakpoints from "assets/theme/base/breakpoints";
import { throttle } from 'lodash';
export function HorizontalTabs({ stickyNavbar=false, tabs=[] }) {
  const [tabsOrientation, setTabsOrientation] = useState("horizontal");
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    const handleTabsOrientation = throttle(() => {
      setTabsOrientation(window.innerWidth < breakpoints.values.sm ? "vertical" : "horizontal");
    }, 200);

    window.addEventListener("resize", handleTabsOrientation);

    handleTabsOrientation();

    return () => window.removeEventListener("resize", handleTabsOrientation);
  }, []);

  const handleSetTabValue = (event, newValue) => setTabValue(newValue);

  return (
      <MDBox mt={stickyNavbar ? 1 : 10}>
        <Grid container>
          <Grid item xs={12} sm={8} lg={4}>
            <AppBar position="static">
              <Tabs orientation={tabsOrientation} value={tabValue} onChange={handleSetTabValue}>
                {tabs.map((tab, index) => (
                  <Tab key={index} label={tab.label} />
                ))}
              </Tabs>
            </AppBar>
          </Grid>
        </Grid>
        <MDBox mt={3}>
          {tabs.map((tab, index) => (
            <MDBox key={index} display={tabValue === index ? "block" : "none"}>
              {tab.content}
            </MDBox>
          ))}
        </MDBox>
      </MDBox>
  );
}

HorizontalTabs.propTypes = {
  stickyNavbar: PropTypes.bool,
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      content: PropTypes.node.isRequired,
    })
  ),
};