import React, { useState } from "react";
import MDBox from "components/atoms/MDBox";
import { Autocomplete, Grid, TextField } from "@mui/material";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import MDButton from "components/atoms/MDButton";
import { Form, Formik } from "formik";
import form from "./schemas/form";
import { ListOfSubject } from "constants/ListOfSubjects/ListOfSubjects";
import FormikDatePicker from "components/atoms/FormikDatePicker/FormikDatePicker.js";
import initialValues from "./schemas/initialValues";
import validations from "./schemas/validations";

// Enable the 'isBetween' plugin
dayjs.extend(isBetween);

function StudentReportForm({ setIsReportData }) {
  const { formId, formField } = form;
  const { startDate, endDate, subjectList } = formField;
  const [initialData, setInitialData] = useState(initialValues);

  const [isLoading, setLoading] = useState(false);

  const [submitLoader, setSubmitLoader] = useState(false);

  const handleSubmit = async (values, actions) => {
    setIsReportData(true);
    actions.setSubmitting(false);
    actions.resetForm();
  };

  return (
    <MDBox p={3} mt={2} minHeight="300px" py={8}>
      <Grid container spacing={3} width={{ xs: "100%", sm: "80%" }} mx="auto">
        <Formik
          initialValues={initialData}
          validationSchema={validations[0]}
          onSubmit={handleSubmit}
          enableReinitialize={true}
          validateOnChange={true}
          validateOnBlur={true}
        >
          {({
            values,
            errors,
            touched,
            isSubmitting,
            setFieldValue,
            setFieldTouched,
          }) => (
            <MDBox mx="auto">
              <Form id={formId} autoComplete="off">
                <MDBox width="100%">
                  <Grid container spacing={5}>
                    {/* Date Pickers */}
                    <Grid item xs={12}>
                      <Grid container spacing={3}>
                        <Grid item xs={12} sm={6}>
                          <FormikDatePicker
                            name={startDate.name}
                            label={startDate.label}
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <FormikDatePicker
                            name={endDate.name}
                            label={endDate.label}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    {/* Subject List Dropdown */}
                    <Grid item xs={12}>
                      <Autocomplete
                        autoSelect
                        autoHighlight
                        multiple
                        options={ListOfSubject}
                        getOptionLabel={(option) => option}
                        value={values.subjectList || []}
                        onChange={(event, newValue) => {
                          setFieldValue(subjectList.name, newValue);
                        }}
                        onBlur={() => setFieldTouched(subjectList.name, true)}
                        className="assignment_dropdown"
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            variant="outlined"
                            label={subjectList.label}
                            placeholder="Select Subjects"
                            error={Boolean(
                              errors.subjectList && touched.subjectList
                            )}
                            helperText={
                              touched.subjectList && errors.subjectList
                            }
                            fullWidth
                          />
                        )}
                      />
                    </Grid>
                  </Grid>

                  <MDButton
                    sx={{ mt: "2rem !important" }}
                    variant="gradient"
                    color="dark"
                    type="submit"
                    disabled={isSubmitting}
                  >
                    Submit
                  </MDButton>
                </MDBox>
              </Form>
            </MDBox>
          )}
        </Formik>
      </Grid>
    </MDBox>
  );
}

export default StudentReportForm;
