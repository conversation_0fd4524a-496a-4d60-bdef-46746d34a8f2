import React, { useState } from "react";
import { Modal } from "components/atoms/Modal/Modal";
import MDButton from "components/atoms/MDButton";
import { Icon } from "@mui/material";
import MDTypography from "components/atoms/MDTypography";
import MDBox from "components/atoms/MDBox";
import StudentReportForm from "./components/StudentReportForm";
import ReportTestList from "./components/ReportTestList";
import Loader from "components/atoms/Loader/Loader";

export const DownloadStudentReportModal = ({
  isStudentReportModalOpen,
  setIsStudentReportModalOpen,
}) => {
  const [isReportData, setIsReportData] = useState(false);
  const [downloadPDF, setDownloadPDF] = useState(false);

  const handleClose = () => {
    setIsStudentReportModalOpen(false);
  };

  return (
    <Modal
      isOpen={isStudentReportModalOpen}
      onClose={() => handleClose()}
      headingText={
        !isReportData ? "Download Student Report" : "Select Test List"
      }
    >
      {isReportData && (
        <MDTypography variant="h6" fontWeight="light">
          Select the tests that you want to include in your student report
        </MDTypography>
      )}
      {!isReportData ? (
        <StudentReportForm setIsReportData={setIsReportData} />
      ) : (
        <ReportTestList
          downloadPDF={downloadPDF}
          setDownloadPDF={setDownloadPDF}
        />
      )}

      {isReportData && (
        <MDBox
          width="100%"
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          p={2}
        >
          <MDButton
            sx={{ mt: "2rem !important" }}
            variant="gradient"
            color="secondary"
            onClick={() => setIsReportData(false)}
          >
            Back
          </MDButton>

          <MDButton
            sx={{ mt: "2rem !important", width: "12rem", py: "8px" }}
            variant="gradient"
            color="primary"
            onClick={() => setDownloadPDF(true)}
          >
            {downloadPDF ? (
              <Loader loaderColor="#fff" />
            ) : (
              <>
                <Icon>file_download</Icon>
                <MDTypography variant="h6" color="white" pl={1} pb={"0.8px"}>
                  Student Report
                </MDTypography>
              </>
            )}
          </MDButton>
        </MDBox>
      )}
    </Modal>
  );
};
