import { useState, useEffect, useContext } from "react";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

export const useSubmissionData = (submissionId, assignmentId) => {
  const navigate = useNavigate();
  const { apiService } = useContext(ApiServiceContext);
  const [submissionData, setSubmissionData] = useState(null);
  const [submissionLoading, setSubmissionLoading] = useState(false);

  const fetchSubmissionData = async () => {
    setSubmissionLoading(true);
    try {
      const responses = await apiService.getSubmissionData(
        submissionId,
        assignmentId
      );
      setSubmissionData(responses);
    } catch (err) {
      toast.error("Error fetching submission data");
      navigate(`/assignments/${assignmentId}?operation=grade`);
    } finally {
      setSubmissionLoading(false);
    }
  };

  // Automatically fetch on mount and when dependencies change
  useEffect(() => {
    fetchSubmissionData();
  }, [submissionId, assignmentId, apiService]);

  return {
    submissionData,
    submissionLoading,
    refetchSubmissionData: fetchSubmissionData,
  };
};
