import React from "react";
import { Card, Grid, Skeleton } from "@mui/material";
import MDBox from "components/atoms/MDBox";

const PopOverChartSkeleton = () => {
  return (
    <Grid item xs={12}>
      <MDBox
        sx={{
          height: "100%",
        }}
      >
        <Card>
          <MDBox height="18rem" borderRadius="12px" px={2}>
            {/* Chart Skeleton */}
            <MDBox py={2} pr={0.5} mt={-5} height={"14.5rem"}>
              <Skeleton variant="rectangular" width="100%" height="100%" />
            </MDBox>
            <MDBox
              pt={1}
              px={1}
              display="flex"
              justifyContent="flex-end"
              flexDirection="column"
            >
              {/* Text skeleton placeholders */}
              <Skeleton
                variant="text"
                width="60%"
                height={30}
                animation="wave"
                sx={{ mb: 1 }}
              />
              <Skeleton
                variant="text"
                width="40%"
                height={20}
                animation="wave"
              />
            </MDBox>
          </MDBox>
        </Card>
      </MDBox>
    </Grid>
  );
};

export default PopOverChartSkeleton;

