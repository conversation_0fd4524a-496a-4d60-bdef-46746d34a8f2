const sampleStudentData = {
  studentData: {
    rollNumber: 321,
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    class: 7,
    section: "B",
  },

  // to show overall stats of student
  studentsOverallStats: {
    totalAssignmentsSolved: 10,
    averageStudentPerformance: 70, // avarage percentage of mark that student got in overall assignments
  },

  // to show monthly data of assignment submissions
  monthlyAssignmentSubmissions: {
    monthlySubmissionsData: [ 7, 8, 9, 1, 0, 5],
  },

  // to show monthly performance of student
  monthlyAssignmentScoreBySubject: {
    data: [
      {
        assessments: [60, 75, 85, 50, 80, 70],
        subjects: "Maths",
      },
      {
        assessments: [90, 80, 60, 95, 50, 70],
        subjects: "Science",
      },
      {
        assessments: [95, 80, 90, 60, 85, 90],
        subjects: "English",
      },
    ],
    months: [
      "Apr 2024",
      "May 2024",
      "Jun 2024",
      "Jul 2024",
      "Aug 2024",
      "Sep 2024",
    ],
    colorMap: {
      "Maths": "info",
      "Science": "primary",
      "English": "dark",
    },
  },

  // avarage percentage of marks in each subject for all assignments
  subjectWisePerformance: {
    labels: ["Maths", "Science", "English", "Hindi"],
    datasets: {
      label: "Scores",
      data: [80, 70, 95, 65],
      backgroundColors: [
        "primary",
        "secondary",
        "info",
        "success",
        "warning",
        "error",
        "light",
        "dark",
      ],
    },
  },
};

export default sampleStudentData;

