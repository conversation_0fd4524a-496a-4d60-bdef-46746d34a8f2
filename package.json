{"name": "eddyowl", "version": "1.0.0", "private": true, "author": "EddyOwl", "dependencies": {"@auth0/auth0-react": "^2.2.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "11.10.8", "@emotion/react": "11.10.8", "@emotion/styled": "^11.13.0", "@fullcalendar/core": "6.1.15", "@fullcalendar/daygrid": "6.1.6", "@fullcalendar/interaction": "6.1.6", "@fullcalendar/react": "6.1.6", "@fullcalendar/timegrid": "6.1.6", "@grafana/faro-react": "^1.10.2", "@grafana/faro-web-tracing": "^1.10.2", "@mui/icons-material": "5.11.16", "@mui/material": "^5.16.7", "@mui/x-date-pickers": "^7.22.1", "@ramonak/react-progress-bar": "^5.3.0", "@react-jvectormap/core": "1.0.4", "@react-jvectormap/world": "1.1.2", "@sentry/cli": "^2.39.1", "@sentry/react": "^8.45.1", "aws-sdk": "^2.1665.0", "chart.js": "4.3.0", "chroma-js": "2.4.2", "compressorjs": "^1.2.1", "dayjs": "^1.11.13", "draft-convert": "^2.1.13", "draft-js": "^0.11.7", "dropzone": "6.0.0-beta.2", "flatpickr": "4.6.13", "formik": "2.2.9", "highcharts": "^11.4.8", "highcharts-react-official": "^3.2.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "immutable": "4.3.7", "pdfjs-dist": "^2.16.105", "preact": "10.12.1", "prop-types": "15.8.1", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-18-image-lightbox": "5.1.4", "react-chartjs-2": "5.2.0", "react-dom": "^18.3.1", "react-draft-wysiwyg": "^1.15.0", "react-flatpickr": "3.10.13", "react-github-btn": "1.4.0", "react-latex-next": "^3.0.0", "react-router-dom": "^6.11.0", "react-scripts": "^5.0.1", "react-table": "7.8.0", "react-toastify": "^10.0.5", "stylis": "4.3.4", "stylis-plugin-rtl": "^2.1.1", "typescript": "4.9.5", "uuid": "^9.0.0", "yup": "1.1.1"}, "scripts": {"start": "set \"GENERATE_SOURCEMAP=false\" && set PORT=3000 && react-scripts start", "build": "set \"GENERATE_SOURCEMAP=false\" && react-scripts build && npm run sentry:sourcemaps && npm run sentry:sourcemaps && npm run sentry:sourcemaps && npm run sentry:sourcemaps", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org eddyowl --project eddyowl_dev ./build && sentry-cli sourcemaps upload --org eddyowl --project eddyowl_dev ./build"}, "proxy": "https://eddyowl.com", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-transform-private-property-in-object": "^7.24.7", "ajv": "^7.2.4"}, "overrides": {"svgo": "3.0.2"}}