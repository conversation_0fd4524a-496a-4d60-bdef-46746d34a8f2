import "katex/dist/katex.min.css";

import React, { useState } from "react";
import { Formik, Form } from "formik";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import { ImageGallery } from "components/molecules/ImageGallery/ImageGallery";
import QuestionsAccordion from "../QuestionsAccordion/QuestionsAccordion";
import renderOutOfScore from "utils/helperFunctions/renderOutOfScore";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import MDButton from "components/atoms/MDButton";
import Loader from "components/atoms/Loader/Loader";

const StudentResponseForm = ({
  initialValues,
  editMode,
  formKey,
  handleSave,
  imageUrls,
  downloadPDF,
  setDownloadPDF,
  setEditMode,
  apiProcessing,
  handleDeleteSubmissionModalOpen,
  handleCancel,
  isStudent,
  showRubric,
}) => {
  const [expanded, setExpanded] = useState(false);

  const handleAccordionChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <>
      <Formik
        key={formKey}
        initialValues={initialValues}
        enableReinitialize
        onSubmit={(values, actions) => {
          handleSave(values, actions);
        }}
      >
        {({ values, getFieldProps }) => (
          <Form>
            <MDBox pl={1} pt={1}>
              {/* form action buttons */}
              {!isStudent && (
                <MDBox
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: { xs: "center", sm: "flex-end" },
                    width: "100%",
                    gap: 2,
                  }}
                >
                  {!editMode ? (
                    <>
                      <MDButton
                        variant="gradient"
                        color="info"
                        onClick={() => setDownloadPDF(true)}
                        disabled={downloadPDF}
                        sx={{ width: "9rem", textWrap: "nowrap" }}
                      >
                        {downloadPDF ? (
                          <Loader loaderColor="#fff" />
                        ) : (
                          "Download PDF"
                        )}
                      </MDButton>
                      <MDButton
                        variant="outlined"
                        color="info"
                        onClick={() => setEditMode(true)}
                        sx={{ width: "6rem" }}
                      >
                        Edit
                      </MDButton>
                      <MDButton
                        variant="outlined"
                        color="error"
                        onClick={() => handleDeleteSubmissionModalOpen()}
                        sx={{ width: "6rem" }}
                      >
                        Delete
                      </MDButton>
                    </>
                  ) : (
                    <>
                      <MDButton
                        variant="gradient"
                        color="info"
                        type="submit"
                        sx={{ width: "6rem" }}
                        disabled={apiProcessing}
                      >
                        {apiProcessing ? <Loader loaderColor="#fff" /> : "Save"}
                      </MDButton>
                      <MDButton
                        variant="outlined"
                        color="error"
                        onClick={handleCancel}
                        sx={{ width: "6rem" }}
                      >
                        Cancel
                      </MDButton>
                    </>
                  )}
                </MDBox>
              )}

              {/* Image Gallery */}
              <MDBox
                width={{ xs: "100%", lg: "55%" }}
                display={{ xs: "none", sm: "flex" }}
                justifyContent="flex-start"
              >
                {imageUrls && <ImageGallery imageUrls={imageUrls} />}
              </MDBox>

              {/* student response list section */}
              <MDBox
                mt={3}
                sx={{
                  "& .MuiPaper-root:first-of-type": {
                    borderRadius: "12px !important",
                  },
                  "& .MuiPaper-root:last-of-type": {
                    borderRadius: "12px !important",
                  },
                }}
              >
                <MDBox
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  mt={{ xs: 2, sm: 0 }}
                >
                  <MDBox display="flex" alignItems="center" gap={2}>
                    <DoneAllIcon fontSize="medium" color="success" />
                    <MDTypography variant="h5" fontWeight="medium" mt={0.4}>
                      Student Responses
                    </MDTypography>
                  </MDBox>
                  <MDBox mr={2}>
                    {renderOutOfScore(
                      initialValues?.totalAchievedScore,
                      initialValues?.totalScore,
                      true
                    )}
                  </MDBox>
                </MDBox>

                {values?.StudentResponseList?.map(
                  (response, index) =>
                    response.question && (
                      <QuestionsAccordion
                        key={index}
                        index={index}
                        expanded={expanded}
                        handleAccordionChange={handleAccordionChange}
                        response={response}
                        editMode={editMode}
                        renderOutOfScore={renderOutOfScore}
                        getFieldProps={getFieldProps}
                        showRubric={showRubric}
                      />
                    )
                )}
              </MDBox>
            </MDBox>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default StudentResponseForm;
