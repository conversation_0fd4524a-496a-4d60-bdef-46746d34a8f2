.avoid-page-break {
  page-break-inside: avoid !important;
  -webkit-column-break-inside: avoid !important;
  break-inside: avoid !important;
}

.avoid-page-break-before {
  page-break-inside: avoid;
  break-inside: avoid;
  -webkit-column-break-inside: avoid;
  page-break-before: auto;
  break-before: auto;
}

.page-break-before {
  page-break-before: always;
}

.page-break-after {
  page-break-after: always;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table-cell {
  border: 1px solid black;
  padding: 10px;
}

.table-header {
  background-color: #f2f2f2;
  font-weight: bold;
}

.page-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.logo {
  width: 200px;
}

.student-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5rem;
}

.student-name {
  color: #00879a;
  font-weight: 600;
  font-size: 1.5rem;
}

.title {
  text-align: center;
  font-weight: 600;
  font-size: 40px;
  margin-bottom: 50px;
}

.divider {
  margin: 20px 0;
  border: 1px solid black;
}

.divider-small {
  margin: 10px 0;
  border: 1px solid black;
}

.text-center {
  text-align: center;
}
