import React, { useState } from "react";
import { Checkbox, Card } from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import StudentReportPdfGenerator from "../StudentReportPdfGenerator";
import studentReportData from "../../data/studentReportData";

// Sample Test Data
const testData = [
  {
    testName: "Math Quiz",
    testSubmissionDate: "12/01/2024",
    isSelected: true,
  },
  {
    testName: "Science Test",
    testSubmissionDate: "13/01/2024",
    isSelected: true,
  },
  {
    testName: "Geography Test",
    testSubmissionDate: "16/01/2024",
    isSelected: true,
  },
];

const ReportTestList = ({ downloadPDF, setDownloadPDF }) => {
  const [tests, setTests] = useState(testData);

  // Handle checkbox toggle and filter items
  const handleCheckboxChange = (testName) => {
    setTests((prevTests) =>
      prevTests.map((test) =>
        test.testName === testName
          ? { ...test, isSelected: !test.isSelected }
          : test
      )
    );
  };

  // Filter out unselected tests
  const selectedTests = tests.filter((test) => test.isSelected);

  const onPdfRenderComplete = () => {
    setDownloadPDF(false);
  };

  return (
    <MDBox sx={{ p: 2 }}>
      <Card
        sx={{
          margin: "10px 0",
          bgcolor: "#fff",
          px: 2,
          py: 1,
        }}
      >
        <MDBox
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
          }}
        >
          <MDTypography variant="h6" fontWeight="medium">
            Select
          </MDTypography>
          <MDTypography variant="h6" fontWeight="medium">
            Assignment Name
          </MDTypography>
          <MDTypography variant="h6" fontWeight="medium">
            Submission Date
          </MDTypography>
        </MDBox>
      </Card>
      <MDBox>
        {tests.map((test) => (
          <Card
            key={test.testName}
            sx={{
              margin: "30px auto",
              bgcolor: "#f1f2f6",
              px: 2,
            }}
          >
            <MDBox
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                p: 2,
              }}
            >
              <Checkbox
                color="secondary"
                checked={test.isSelected}
                onChange={() => handleCheckboxChange(test.testName)}
              />

              <MDTypography variant="h6" fontWeight="medium" textAlign="left">
                {test.testName}
              </MDTypography>
              <MDTypography variant="h6" fontWeight="medium">
                {test.testSubmissionDate}
              </MDTypography>
            </MDBox>
          </Card>
        ))}
      </MDBox>
      <MDBox sx={{ display: "none" }}>
        {downloadPDF && (
          <StudentReportPdfGenerator
            triggerDownload={downloadPDF}
            data={studentReportData}
            onRenderComplete={onPdfRenderComplete}
          />
        )}
      </MDBox>
    </MDBox>
  );
};

export default ReportTestList;
