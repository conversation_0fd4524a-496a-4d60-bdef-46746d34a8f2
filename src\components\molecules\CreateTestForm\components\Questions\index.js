import PropTypes from "prop-types";
import { Field<PERSON>rray, Field, useFormikContext } from "formik";
import Grid from "@mui/material/Grid";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import MDButton from "components/atoms/MDButton";
import {
  InputLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Icon,
} from "@mui/material";
import { useState, useContext, useEffect, useRef } from "react";
import { Modal } from "components/atoms/Modal/Modal";
import { toast } from "react-toastify";
import { ApiServiceContext } from "context";
import { v4 } from "uuid";
import Loader from "components/atoms/Loader/Loader";
import { compressAndUploadFiles } from "utils/helperFunctions/compressAndUploadFiles";
import { convertPdfToImages } from "utils/helperFunctions/convertPdfToImages";
import { LightBox } from "components/molecules/LightBox/LightBox";
import FormField from "components/atoms/FormField";
import { TestInfo } from "../TestInfo";
import GenerateRubricScreen from "../GenerateRubricScreen/GenerateRubricScreen";
import PreviewsDraggableArea from "../PreviewsDraggableArea/PreviewsDraggableArea";
import { cleanupUploadedFiles } from "utils/helperFunctions/cleanupUploadedFiles";

export function Questions({
  formType,
  formData,
  viewOnly,
  isValidScore,
  setIsValidScore,
  errorList,
  checkFieldErrors,
  rubricLoading,
  setRubricLoading,
  isRubricGenerated,
  setIsRubricGenerated,
  hasRubricErrors,
  isShowRubricScreen,
  setIsShowRubricScreen,
  isNewQuestionAdded,
  setIsNewQuestionAdded,
  isValuesChanged,
  setIsValuesChanged,
  setActiveStep,
  isRubricButtonClicked,
  setIsRubricButtonClicked,
  isRubricFromFiles,
  setIsRubricFromFiles,
  scrollToBottom,
  activeStep,
  isAutoFillAssignment,
  setIsAutoFillAssignment,
}) {
  const { apiService } = useContext(ApiServiceContext);
  const { values, errors, touched } = formData;
  const { setFieldValue } = useFormikContext();

  const [isUploadModalOpen, setUploadModalOpen] = useState(false);
  const [files, setFiles] = useState([]);
  const [error, setError] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [previews, setPreviews] = useState([]);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [photoIndex, setPhotoIndex] = useState(0);

  const [editOnly, setIsEditAssignment] = useState(false);
  const [calculatedScore, setCalculatedScore] = useState(0);

  const [pdfLoading, setPdfLoading] = useState(false);

  const firstRubricRef = useRef(null);
  const [scrollRubricIndex, setScrollRubricIndex] = useState(0);

  const lastQuestionsRef = useRef(values?.questions || []);

  useEffect(() => {
    const currentUrl = window.location.href;
    const pattern = /\/assignments\/edit-assignment\/[0-9a-fA-F-]{36}$/;
    if (pattern.test(currentUrl)) {
      setIsEditAssignment(true);
    } else {
      setIsEditAssignment(false);
    }

    // for the first question to be start from 1
    values.questions[0].questionNumber = 1;
  }, []);

  useEffect(() => {
    if (!isRubricGenerated && activeStep === 0 && !isRubricButtonClicked)
      return;

    const currentQuestions = values?.questions || [];
    const lastQuestions = lastQuestionsRef.current;

    const hasChanges =
      lastQuestions.length !== currentQuestions.length ||
      currentQuestions.some((newItem, index) => {
        const oldItem = lastQuestions[index];
        return (
          oldItem?.questionNumber !== newItem.questionNumber ||
          oldItem?.question !== newItem.question ||
          oldItem?.questionScore !== newItem.questionScore
        );
      });

    setIsValuesChanged(hasChanges);

    if (hasChanges || isRubricGenerated) {
      lastQuestionsRef.current = currentQuestions; // Update ref if values have changed
    }
  }, [values.questions, isRubricGenerated, activeStep, isRubricButtonClicked]);

  const handleFileChange = async (event) => {
    setPdfLoading(true);

    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "application/pdf",
    ];
    const allFiles = [...event.target.files];

    // Step 1: Filter only allowed files
    const validFiles = allFiles.filter((file) =>
      allowedTypes.includes(file.type)
    );

    const newFilesList = [];
    const newPreviews = [];

    // Helper function to handle file reading and updating previews
    const readFile = (file) => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          newPreviews.push(reader.result); // Add preview to the array
          resolve();
        };
        reader.readAsDataURL(file);
      });
    };

    // Process each file
    for (const file of validFiles) {
      if (file.type === "application/pdf") {
        // If PDF, convert to images first, then process images
        const pdfImages = await convertPdfToImages(file);
        for (const imageFile of pdfImages) {
          newFilesList.push(imageFile); // Add converted image files to list
          await readFile(imageFile); // Read the image file as preview
        }
      } else {
        // Non-PDF file, just add it to the list and process preview
        newFilesList.push(file);
        await readFile(file); // Read the non-PDF file as preview
      }
    }

    // Now, update the state with all files and previews
    setFiles((prevFiles) => [...prevFiles, ...newFilesList]);
    setPreviews((prevPreviews) => [...prevPreviews, ...newPreviews]);

    setPdfLoading(false);
  };

  const handleRemoveFile = (index) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
    const newPreviews = [...previews];
    newPreviews.splice(index, 1);
    setPreviews(newPreviews);
  };

  const addDefaultValues = (data) => {
    return {
      ...data,
      questionList: data.questionList.map((question) => ({
        ...question,
        questionScore: question.questionScore ?? 0,
        questionRubric: question.questionRubric || "",
      })),
    };
  };

  const clearQuestionRubrics = (questions) => {
    return questions.map((question) => ({
      ...question,
      questionRubric: "", // Set questionRubric to an empty string
    }));
  };

  const abortControllerRef = useRef(null);
  const isCancelledRef = useRef(false);
  const tmpFileListRef = useRef([]);
  const [isFileUploadStarted, setIsFileUploadStarted] = useState(false);

  const handleClose = () => {
    // Cancel the ongoing fetch request using AbortController if it exists
    if (isFileUploadStarted && abortControllerRef.current) {
      abortControllerRef.current.abort();
      isCancelledRef.current = true;
    }

    // Reset state values
    setFiles([]);
    setPreviews([]);
    setUploadModalOpen(false);
    setPdfLoading(false);
    setIsLoading(false);
    !isRubricFromFiles && setIsRubricFromFiles(false);

    // Reset cancellation state to clean up
    abortControllerRef.current = null;
    setIsFileUploadStarted(false);
    isCancelledRef.current = false;
  };

  const handleFileSubmit = async () => {
    if (!viewOnly && !editOnly) {
      if (files.length === 0) {
        toast.error("Please select at least one file.");
        return;
      }

      setIsLoading(true);
      setIsFileUploadStarted(true);

      // Create a new AbortController and store it in the ref
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const signal = abortController.signal;

      const assignUniqueId = v4();

      const fileLocationGenerator = (file, index) => {
        const fileExtension = file.name.split(".").pop();
        return `${apiService.instituteId}/temp/${assignUniqueId}-page-${
          index + 1
        }.${fileExtension}`;
      };

      // Upload and compress files
      const { success, tmpFileList, errorFiles } = await compressAndUploadFiles(
        files,
        fileLocationGenerator,
        signal,
        tmpFileListRef
      );

      if (signal?.aborted) {
        //
        await cleanupUploadedFiles(tmpFileList); // Clean up uploaded files
        setIsFileUploadStarted(false);
        setIsLoading(false);
        return;
      }

      if (isCancelledRef.current) {
        setIsFileUploadStarted(false);
        return;
      }

      if (!success) {
        if (!signal?.aborted) toast.error("Error uploading files.");
        setError(
          `Unable to upload the following files: ${errorFiles.join(", ")}`
        );
        setIsLoading(false);
        setPreviews([]);
        setFiles([]);
        setUploadModalOpen(false);
        return;
      }
      try {
        if (!isRubricFromFiles) {
          // Call generateTestAuto with cancellation signal
          const res = await apiService.generateAssignmentFromFiles(tmpFileList, signal);

          if (signal?.aborted) {
            //
            await cleanupUploadedFiles(tmpFileListRef);
            setIsFileUploadStarted(false);
            setIsLoading(false);
            return;
          }

          const updatedQuestions = addDefaultValues(res);
          values.questions = updatedQuestions.questionList;
          toast.success("Assignment data loaded successfully.");
          setIsAutoFillAssignment(true);
        } else {
          // Handle rubric generation from files
          values.questions = clearQuestionRubrics(values?.questions);

          const questionList = values?.questions;
          const inputData = {
            subject: values?.subjectName,
            class: values?.class,
            questions: { questionList },
            filePaths: tmpFileList,
          };

          const rubricsData = await apiService.generateRubricFromFiles(
            inputData,
            signal
          );

          if (signal?.aborted) {
            //
            await cleanupUploadedFiles(tmpFileListRef);
            setIsFileUploadStarted(false);
            setIsLoading(false);
            return;
          }

          const rubricMap = new Map(
            rubricsData?.questionList?.map(
              ({ questionNumber, questionRubric }) => [
                questionNumber,
                questionRubric,
              ]
            )
          );

          // Further processing and updating the state
          const firstQuestionIndex =
            questionList.findIndex(
              (question) =>
                question.questionNumber ===
                rubricsData?.questionList?.[0]?.questionNumber
            ) ?? 0;

          setScrollRubricIndex(firstQuestionIndex);
          setRubricLoading(false);
          setIsRubricFromFiles(false);
          setIsRubricGenerated(true);
          setUploadModalOpen(false);
          setIsShowRubricScreen(false);
          setIsRubricButtonClicked(true);
          isValuesChanged && setIsValuesChanged(false);

          setExpandedItems(Array(values?.questions?.length).fill(true));
          setExpandAll(true);

          await new Promise((resolve) => setTimeout(resolve, 500));

          if (firstRubricRef.current) {
            firstRubricRef.current.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }

          let firstRendered = false;

          for (const [index, question] of values?.questions?.entries()) {
            const rubricText = rubricMap.get(question.questionNumber);
            if (rubricText) {
              if (!firstRendered) {
                firstRendered = true;
                let currentText = "";
                const words = rubricText.split(" ");

                for (let i = 0; i < words.length; i++) {
                  currentText += ` ${words[i]}`;
                  setFieldValue(
                    `questions.${index}.questionRubric`,
                    currentText
                  );
                  await new Promise((resolve) => setTimeout(resolve, 100)); // Word typing delay
                }
              } else {
                setFieldValue(`questions.${index}.questionRubric`, rubricText);
              }
            }
          }

          toast.success("Rubric generated successfully!");
        }
      } catch (err) {
        if (err.name === "AbortError") {
          // toast.error("Request cancelled during API call.");
        } else {
          setError(err);
          if (isRubricFromFiles) {
            toast.error("Error generating rubric, please try again!");
          } else {
            toast.error("Error processing files.");
          }
        }
      } finally {
        isUploadModalOpen && setUploadModalOpen(false);
        setIsLoading(false);
        setPdfLoading(false);
        setPreviews([]);
        setFiles([]);
        !isRubricFromFiles && setIsRubricFromFiles(false);

        setIsFileUploadStarted(false);
        abortControllerRef.current = null; // Reset AbortController
        isCancelledRef.current = false; // Ensure flag is reset
        tmpFileListRef.current = [];
      }
    }
  };

  const openLightbox = (index) => {
    setPhotoIndex(index);
    setIsLightboxOpen(true);
  };

  const calculateTotalQuestionScore = () => {
    const total =
      values?.questions?.reduce((accumulator, question) => {
        const score = Number(question.questionScore);
        return !isNaN(score) ? accumulator + score : accumulator;
      }, 0) || 0;

    setCalculatedScore(total);
    setIsValidScore(total === values?.totalScore);
  };

  useEffect(() => {
    calculateTotalQuestionScore();
  }, [values?.questions, values?.totalScore]);

  const isErrorPresent = (index) => {
    const isError = errorList.includes(index);

    return isError;
  };

  useEffect(() => {
    checkFieldErrors(values?.questions);
  }, [values?.questions, checkFieldErrors, hasRubricErrors]);

  const [expandedItems, setExpandedItems] = useState([]);
  const [expandAll, setExpandAll] = useState(false);
  const questionRubricRefs = useRef([]);

  useEffect(() => {
    if (
      formType === "add" &&
      values?.questions?.length > expandedItems.length
    ) {
      // Expand the newly added question by default
      setExpandedItems((prev) => [...prev, true]);

      if (values?.questions?.length > 1 && !isAutoFillAssignment) {
        setTimeout(() => {
          scrollToBottom();
        }, 300);
      }
    }
  }, [values?.questions?.length, expandedItems, isAutoFillAssignment]);

  useEffect(() => {
    if (expandAll) {
      setExpandedItems(Array(values?.questions?.length).fill(true));
    }
  }, [expandAll]);

  const handleGenerateRubrics = async () => {
    setRubricLoading(true);

    values.questions = clearQuestionRubrics(values?.questions);

    const questionList = values?.questions;

    try {
      const inputData = {
        subject: values?.subjectName,
        class: values?.class,
        questions: { questionList },
      };

      const rubricsData = await apiService.generateRubricFromAi(inputData);

      const rubricMap = new Map(
        rubricsData?.questionList?.map(({ questionNumber, questionRubric }) => [
          questionNumber,
          questionRubric,
        ])
      );

      const firstQuestionIndex =
        questionList.findIndex(
          (question) =>
            question.questionNumber ===
            rubricsData?.questionList?.[0]?.questionNumber
        ) ?? 0;

      setScrollRubricIndex(firstQuestionIndex);
      setRubricLoading(false);
      setIsShowRubricScreen(false);
      setIsRubricGenerated(true);
      setIsRubricButtonClicked(true);
      isValuesChanged && setIsValuesChanged(false);

      // Set expanded state for all items
      setExpandedItems(Array(values?.questions?.length).fill(true));
      setExpandAll(true);

      // Wait for accordion expansion
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Scroll to the first rubric
      if (firstRubricRef.current) {
        firstRubricRef.current.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }

      let firstRendered = false;

      // Process each question
      for (const [index, question] of values?.questions?.entries()) {
        const rubricText = rubricMap.get(question.questionNumber);
        if (rubricText) {
          // Handle word-by-word typing for the first question
          if (!firstRendered) {
            firstRendered = true;
            let currentText = "";
            const words = rubricText.split(" ");

            // Type word-by-word for the first question only
            for (let i = 0; i < words.length; i++) {
              currentText += ` ${words[i]}`;
              setFieldValue(`questions.${index}.questionRubric`, currentText);
              await new Promise((resolve) => setTimeout(resolve, 100)); // Word typing delay
            }
          } else {
            // For other questions, set the full rubric text
            setFieldValue(`questions.${index}.questionRubric`, rubricText);
          }
        }
      }
      toast.success("Rubric generated successfully!");
    } catch (err) {
      setError(err);
      setRubricLoading(false);
      toast.error("Error generating rubric, please try again!");
    }
  };

  return (
    <MDBox>
      {/* auto fill from files modal */}
      <Modal
        isOpen={isUploadModalOpen}
        onClose={handleClose}
        headingText={
          !isRubricFromFiles
            ? "Upload Assignment Files"
            : "Upload Rubrics Files"
        }
      >
        <MDBox
          mt={2}
          display="flex"
          flexDirection="column"
          justifyContent="space-between"
          minHeight="15rem"
        >
          <MDBox
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            flex="1"
          >
            <MDButton
              variant="gradient"
              color="info"
              sx={{ width: "200px", padding: "0", marginBottom: "10px" }}
            >
              <InputLabel
                sx={{
                  width: "220px",
                  padding: "10px",
                  justifyContent: "center",
                  cursor: "pointer",
                }}
                color="primary"
              >
                <input
                  key={files.length - 1}
                  type="file"
                  multiple
                  accept=".jpg,.jpeg,.png,.pdf"
                  onChange={handleFileChange}
                  className="mb-4"
                  class="hidden"
                />
                <MDTypography
                  variant="h6"
                  color="white"
                  fontWeight="regular"
                  px={2}
                  width="100%"
                >
                  {files.length === 0 ? "Choose Files" : "Choose More Files"}
                </MDTypography>
              </InputLabel>
            </MDButton>
            {/* Drag and drop area for Previews */}
            <PreviewsDraggableArea
              previews={previews}
              handleRemoveFile={handleRemoveFile}
              pdfLoading={pdfLoading}
              openLightbox={openLightbox}
              setPreviews={setPreviews}
              setFiles={setFiles}
            />
          </MDBox>
          <MDBox className="flex justify-between mt-5" width="100%">
            <MDButton variant="outlined" color="error" onClick={handleClose}>
              Cancel
            </MDButton>
            <MDButton
              variant="gradient"
              color="dark"
              onClick={handleFileSubmit}
              disabled={files.length === 0 || isLoading}
              sx={{ width: "9rem" }}
            >
              {isLoading ? <Loader loaderColor="#fff" /> : "Submit"}
            </MDButton>
          </MDBox>
        </MDBox>
      </Modal>

      {/* assignment info form */}

      <>
        {(formType === "add" &&
          !isShowRubricScreen &&
          !isRubricButtonClicked) ||
        formType !== "add" ? (
          <>
            <TestInfo
              formData={formData}
              viewOnly={viewOnly}
              formType={formType}
            />
            <hr
              style={{
                border: "none",
                borderTop: `2px solid #a9a9a9`,
                width: "90%",
                margin: "0 auto",
                marginTop: "45px",
              }}
            />
          </>
        ) : null}
      </>

      {/* question form section */}

      {isShowRubricScreen ? (
        <GenerateRubricScreen
          rubricLoading={rubricLoading}
          handleGenerateRubrics={() => handleGenerateRubrics(values?.questions)}
          values={values}
          setUploadModalOpen={setUploadModalOpen}
          setIsShowRubricScreen={setIsShowRubricScreen}
          setIsRubricFromFiles={setIsRubricFromFiles}
          isValuesChanged={isValuesChanged}
          setIsValuesChanged={setIsValuesChanged}
          setActiveStep={setActiveStep}
          setIsRubricGenerated={setIsRubricGenerated}
          setIsRubricButtonClicked={setIsRubricButtonClicked}
        />
      ) : (
        <MDBox
          sx={{
            height: "100%",
            overflowY: "auto",
            p: 3,
            mt: 2,
          }}
        >
          <MDBox
            display="flex"
            justifyContent="space-between"
            sx={{
              flexDirection: { xs: "column", sm: "row" },
              alignItems: { xs: "flex-start", sm: "center" },
            }}
          >
            <MDBox display="flex" flexDirection="column" gap={1}>
              <MDTypography variant="h5" fontWeight="bold">
                Questions
              </MDTypography>

              {isRubricButtonClicked && (
                <MDTypography variant="button" fontWeight="regular">
                  click on each rubric field to add manually
                </MDTypography>
              )}

              {!isRubricButtonClicked && (
                <MDBox
                  display="flex"
                  alignItems="center"
                  gap={1}
                  fontWeight="regular"
                  sx={{
                    fontSize: { xs: "13px", sm: "16px" },
                  }}
                >
                  {`Total Score: `}
                  <>
                    {values?.totalScore ? (
                      <>
                        <MDTypography
                          variant="h4"
                          color={isValidScore ? "success" : "error"}
                          fontWeight="medium"
                          fontSize="1.25rem"
                        >
                          {calculatedScore}
                        </MDTypography>
                        <MDTypography
                          variant="h4"
                          fontWeight="medium"
                          fontSize="1.2rem"
                        >
                          {`/ ${values?.totalScore}`}
                        </MDTypography>
                      </>
                    ) : (
                      <MDTypography
                        variant="h4"
                        fontWeight="medium"
                        fontSize="1.2rem"
                      >
                        N/A
                      </MDTypography>
                    )}
                  </>
                </MDBox>
              )}
            </MDBox>
            {!viewOnly && !editOnly && !isRubricButtonClicked && (
              <MDButton
                variant="outlined"
                color="info"
                sx={{
                  mt: { xs: 2, md: 0 },
                  mr: { xs: 0, md: "10px" },
                  width: { xs: "100%", sm: "auto" },
                  display: "flex",
                  alignItems: "center",
                }}
                onClick={() => {
                  setUploadModalOpen(true);
                }}
              >
                <Icon sx={{ marginRight: "3px" }}>image</Icon>
                Auto-fill from files
              </MDButton>
            )}
          </MDBox>
          <FieldArray
            name="questions"
            render={(arrayHelpers) => (
              <MDBox
                mt={4}
                sx={{
                  "& .MuiPaper-root:first-of-type": {
                    borderRadius: "12px !important",
                  },
                  "& .MuiPaper-root:last-of-type": {
                    borderRadius: "12px !important",
                  },
                }}
              >
                {values.questions.map((question, index) => (
                  <Accordion
                    key={index}
                    defaultExpanded={expandedItems[index] === true}
                    expanded={expandedItems[index] === true}
                    TransitionProps={{ unmountOnExit: false }} // Add this line
                    onChange={(e, expanded) => {
                      const newExpandedItems = [...expandedItems];
                      newExpandedItems[index] = expanded;
                      setExpandedItems(newExpandedItems);
                    }}
                    sx={{
                      marginTop: "30px",
                      borderRadius: "12px",
                      overflow: "hidden !important",
                      boxShadow:
                        hasRubricErrors && isErrorPresent(index) ? 0 : 2,
                      border: "1px solid",
                      borderColor:
                        hasRubricErrors && isErrorPresent(index)
                          ? "red"
                          : "inherit",
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<Icon>expand_more</Icon>}
                      aria-controls={`panel${index}-content`}
                      id={`panel${index}-header`}
                      sx={{
                        borderRadius: "12px",
                      }}
                    >
                      <MDTypography
                        variant="h6"
                        sx={{
                          color:
                            hasRubricErrors && isErrorPresent(index)
                              ? "red"
                              : "inherit",
                        }}
                      >
                        {values.questions[index].questionNumber
                          ? `Question ${values.questions[index].questionNumber}`
                          : `Question ${index + 1}`}
                      </MDTypography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <MDBox>
                        <Grid
                          container
                          spacing={3}
                          mt={
                            isRubricButtonClicked && formType === "add"
                              ? -13
                              : -3
                          }
                        >
                          <Grid item xs={12} sm={5}>
                            {!isRubricButtonClicked && (
                              <Field
                                name={`questions.${index}.questionNumber`}
                                render={({ field }) => (
                                  <FormField
                                    {...field}
                                    type="number"
                                    label="Question Number"
                                    disabled={viewOnly}
                                    error={
                                      errors.questions &&
                                      errors.questions[index] &&
                                      errors.questions[index].questionNumber &&
                                      touched.questions &&
                                      touched.questions[index] &&
                                      touched.questions[index].questionNumber
                                    }
                                    success={
                                      question.questionNumber.length > 0 &&
                                      !errors.questions &&
                                      !errors.questions[index] &&
                                      !errors.questions[index].questionNumber
                                    }
                                    isFixedHeading={true}
                                  />
                                )}
                              />
                            )}
                          </Grid>
                          <Grid
                            item
                            xs={12}
                            sm={
                              formType === "add" && isRubricButtonClicked
                                ? 12
                                : 5
                            }
                          >
                            {!isRubricButtonClicked ? (
                              <Field
                                name={`questions.${index}.questionScore`}
                                render={({ field }) => (
                                  <FormField
                                    {...field}
                                    type="number"
                                    label="Question Score"
                                    disabled={viewOnly}
                                    error={
                                      errors.questions &&
                                      errors.questions[index] &&
                                      errors.questions[index].questionScore &&
                                      touched.questions &&
                                      touched.questions[index] &&
                                      touched.questions[index].questionScore
                                    }
                                    success={
                                      question.questionScore.length > 0 &&
                                      !errors.questions &&
                                      !errors.questions[index] &&
                                      !errors.questions[index].questionScore
                                    }
                                    isFixedHeading={true}
                                  />
                                )}
                              />
                            ) : (
                              <MDBox
                                sx={{
                                  ml: "auto",
                                  mr: 5,
                                  display: "flex",
                                  alignItems: "center",
                                  width: "5rem",
                                  gap: 1,
                                }}
                              >
                                <MDTypography
                                  variant="h6"
                                  fontWeight="medium"
                                  color="black"
                                >
                                  Score:
                                </MDTypography>
                                <MDTypography
                                  variant="h5"
                                  fontWeight="medium"
                                  color="black"
                                >
                                  {question?.questionScore}
                                </MDTypography>
                              </MDBox>
                            )}
                          </Grid>
                          <Grid item xs={12}>
                            {!isRubricButtonClicked ? (
                              <Field
                                name={`questions.${index}.question`}
                                render={({ field }) => (
                                  <FormField
                                    {...field}
                                    type="text"
                                    label="Question"
                                    multiline
                                    disabled={viewOnly}
                                    error={
                                      errors.questions &&
                                      errors.questions[index] &&
                                      errors.questions[index].question &&
                                      touched.questions &&
                                      touched.questions[index] &&
                                      touched.questions[index].question
                                    }
                                    success={
                                      String(question.question).length > 0 &&
                                      !(
                                        errors.questions &&
                                        errors.questions[index] &&
                                        errors.questions[index].question
                                      )
                                    }
                                    isFixedHeading={true}
                                  />
                                )}
                              />
                            ) : (
                              <MDTypography
                                variant="h6"
                                fontWeight="medium"
                                mt={isRubricButtonClicked && -1}
                              >
                                {question?.question}
                              </MDTypography>
                            )}
                          </Grid>
                          {(formType === "add" && isRubricButtonClicked) ||
                          formType !== "add" ? (
                            <Grid item xs={12}>
                              {/* Added ref to scroll to that particular generated rubric */}
                              <div
                                ref={
                                  index === scrollRubricIndex
                                    ? firstRubricRef
                                    : null
                                }
                              >
                                <Field
                                  name={`questions.${index}.questionRubric`}
                                  render={({ field }) => (
                                    <FormField
                                      {...field}
                                      type="text"
                                      label="Question Rubric"
                                      disabled={viewOnly}
                                      multiline
                                      error={
                                        errors.questions &&
                                        errors.questions[index] &&
                                        errors.questions[index]
                                          .questionRubric &&
                                        touched.questions &&
                                        touched.questions[index] &&
                                        touched.questions[index].questionRubric
                                      }
                                      success={
                                        question.questionRubric &&
                                        typeof question.questionRubric ===
                                          "string" &&
                                        question.questionRubric.length > 0 &&
                                        !(
                                          errors.questions &&
                                          errors.questions[index] &&
                                          errors.questions[index].questionRubric
                                        )
                                      }
                                      ref={(el) =>
                                        (questionRubricRefs.current[index] = el)
                                      } // Assign ref
                                      isFixedHeading={true}
                                    />
                                  )}
                                />
                              </div>
                            </Grid>
                          ) : null}

                          {!viewOnly &&
                            values.questions.length > 1 &&
                            !isRubricButtonClicked && (
                              <Grid item xs={12} sm={3}>
                                <MDButton
                                  type="button"
                                  onClick={() => {
                                    isNewQuestionAdded &&
                                      setIsNewQuestionAdded(false);

                                    // Update expandedItems state to match the reduced questions length
                                    setExpandedItems((prev) =>
                                      prev.filter((_, i) => i !== index)
                                    );

                                    arrayHelpers.remove(index);
                                  }}
                                  variant="gradient"
                                  color="error"
                                >
                                  Remove
                                </MDButton>
                              </Grid>
                            )}
                        </Grid>
                      </MDBox>
                    </AccordionDetails>
                  </Accordion>
                ))}
                {!viewOnly && !isRubricButtonClicked && (
                  <MDBox
                    mt={5}
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                    px={1}
                  >
                    <MDButton
                      type="button"
                      onClick={() => {
                        isRubricGenerated && setIsRubricGenerated(false);
                        !isNewQuestionAdded && setIsNewQuestionAdded(true);
                        isAutoFillAssignment && setIsAutoFillAssignment(false);

                        const lastQuestionNumber =
                          values.questions.length > 1
                            ? values.questions[values.questions.length - 1]
                                .questionNumber
                            : 1;

                        arrayHelpers.push({
                          questionNumber: lastQuestionNumber + 1,
                          question: "",
                          questionScore: 0,
                          questionRubric: "",
                        });
                      }}
                      variant="gradient"
                      color="info"
                      sx={{
                        position: "absolute",
                        bottom: 40,
                        left: 40,
                        zIndex: 51,
                      }}
                    >
                      Add Question
                    </MDButton>
                  </MDBox>
                )}
              </MDBox>
            )}
          />
        </MDBox>
      )}

      {isLightboxOpen && (
        <LightBox
          previews={previews}
          setIsLightboxOpen={setIsLightboxOpen}
          setPhotoIndex={setPhotoIndex}
          photoIndex={photoIndex}
        />
      )}
    </MDBox>
  );
}

Questions.propTypes = {
  formData: PropTypes.oneOfType([PropTypes.object, PropTypes.func]).isRequired,
  isValidScore: PropTypes.oneOf([true, false]),
  setIsValidScore: PropTypes.func,
};
