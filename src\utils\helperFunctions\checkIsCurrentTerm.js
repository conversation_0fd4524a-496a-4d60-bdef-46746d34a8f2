import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import customParseFormat from "dayjs/plugin/customParseFormat";
import DISPLAY_DATE_FORMAT from "constants/DISPLAY_DATE_FORMAT/DISPLAY_DATE_FORMAT";

// Extend dayjs with required plugins
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
dayjs.extend(customParseFormat);

const checkIsCurrentTerm = (start, end) => {
  try {
    // First try to parse with DD-MM-YYYY format
    let termStart = dayjs(start, "DD-MM-YYYY");
    let termEnd = dayjs(end, "DD-MM-YYYY");

    // If parsing failed, try with the display format
    if (!termStart.isValid()) {
      termStart = dayjs(start, DISPLAY_DATE_FORMAT);
    }
    if (!termEnd.isValid()) {
      termEnd = dayjs(end, DISPLAY_DATE_FORMAT);
    }

    // If still invalid, try parsing as ISO
    if (!termStart.isValid()) {
      termStart = dayjs(start);
    }
    if (!termEnd.isValid()) {
      termEnd = dayjs(end);
    }

    // If dates are still invalid, return false
    if (!termStart.isValid() || !termEnd.isValid()) {
      return false;
    }

    termStart = termStart.startOf("day");
    termEnd = termEnd.endOf("day");
    const today = dayjs().startOf("day");

    return today.isSameOrAfter(termStart) && today.isSameOrBefore(termEnd);
  } catch (error) {
    return false;
  }
};

export default checkIsCurrentTerm;

