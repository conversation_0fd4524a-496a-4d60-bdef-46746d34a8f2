/**
 * Generates data for assessments by class.
 * @param {Array} classes - Array of class names or labels.
 * @param {Array} assessments - Array of assessment data corresponding to each class.
 * @param {Array} students - Array of student data corresponding to each class.
 * @returns {Object} Object containing labels, datasets, and students data.
 */
const assessmentsByClassData = (classes, assessments, students) => {
  return {
    labels: classes,
    datasets: [
      {
        label: "Assignments",
        color: "success",
        data: assessments,
      },
    ],
    students: students,
  };
};

export default assessmentsByClassData;
