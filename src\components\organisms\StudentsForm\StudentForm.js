import React from "react";
import { Formik, Form } from "formik";
import Grid from "@mui/material/Grid";
import FormField from "components/atoms/FormField";
import validations from "./schemas/validations";
import form from "./schemas/form";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import Loader from "components/atoms/Loader/Loader";
import MDBox from "components/atoms/MDBox";

export function StudentForm({
  studentData,
  handleFormSubmit,
  submitStudentLoader,
  formType,
}) {
  const { formId, formField } = form;

  const {
    firstName,
    lastName,
    studentId,
    rollNumber,
    class: studentClass,
    section,
    email,
  } = formField;

  const initialFormValues = studentData || {
    firstName: "",
    lastName: "",
    studentId: "",
    rollNumber: "",
    class: "",
    section: "",
    email: "",
  };

  const fields = [
    { ...firstName, gridProps: { xs: 12, sm: 6 } },
    { ...lastName, gridProps: { xs: 12, sm: 6 } },
    {
      ...studentId,
      gridProps: { xs: 12, sm: 6 },
      disabled: formType === "edit",
      inputProps: { style: { textTransform: "uppercase" } },
      transform: (value) => (value ? value.toUpperCase() : ""),
    },
    {
      ...rollNumber,
      gridProps: { xs: 12, sm: 6 },
      transform: (value) => (value ? Number(value) : ""),
    },
    {
      ...studentClass,
      gridProps: { xs: 12, sm: 6 },
      transform: (value) => (value ? Number(value) : ""),
    },
    {
      ...section,
      gridProps: { xs: 12, sm: 6 },
      inputProps: { style: { textTransform: "uppercase" } },
      transform: (value) => (value ? value.toUpperCase() : ""),
    },
    { ...email, gridProps: { xs: 12, sm: 6 } },
  ];

  return (
    <Grid container sx={{ height: "100%", mt: 2 }}>
      <Grid item xs={12}>
        <Formik
          initialValues={initialFormValues}
          validationSchema={validations}
          onSubmit={handleFormSubmit}
          enableReinitialize={true}
          validateOnBlur={true}
          validateOnChange={true}
        >
          {({ values, errors, touched, handleChange }) => (
            <Form id={formId} autoComplete="off">
              <Grid container spacing={2}>
                {fields.map((field) => (
                  <Grid item key={field.name} {...field.gridProps}>
                    <FormField
                      type={field.type}
                      label={field.label}
                      name={field.name}
                      value={
                        field.transform && values[field.name] !== undefined
                          ? field.transform(values[field.name])
                          : values[field.name]
                      }
                      placeholder={field.placeholder}
                      error={errors[field.name] && touched[field.name]}
                      success={
                        values[field.name]?.length > 0 && !errors[field.name]
                      }
                      disabled={
                        formType === "edit" && field.name === "studentId"
                      }
                      required={field.name !== "email"}
                      inputProps={field.inputProps}
                      onChange={(e) => {
                        const value = field.transform
                          ? field.transform(e.target.value)
                          : e.target.value;
                        handleChange({
                          target: {
                            name: field.name,
                            value,
                          },
                        });
                      }}
                    />
                  </Grid>
                ))}
              </Grid>

              <MDTypography
                fontWeight="light"
                variant="button"
                className="flex justify-between"
                my={2}
              >
                Fields with * are mandatory
              </MDTypography>

              <MDBox
                display="flex"
                justifyContent="flex-end"
                alignItems="center"
              >
                <MDButton
                  type="submit"
                  variant="gradient"
                  color="dark"
                  sx={{ marginTop: "20px", width: "8rem" }}
                  disabled={submitStudentLoader}
                >
                  {submitStudentLoader ? (
                    <Loader loaderColor="#fff" />
                  ) : (
                    "Submit"
                  )}
                </MDButton>
              </MDBox>
            </Form>
          )}
        </Formik>
      </Grid>
    </Grid>
  );
}
