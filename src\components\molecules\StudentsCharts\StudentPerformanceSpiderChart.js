import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsDrilldown from "highcharts/modules/drilldown";
import HighchartsReact from "highcharts-react-official";
import Loader from "components/atoms/Loader/Loader";
import HC_more from "highcharts/highcharts-more";
import studentPerformanceData from "data/studentPerformanceData";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import RestartAltIcon from "@mui/icons-material/RestartAlt";

HighchartsDrilldown(Highcharts);
HC_more(Highcharts);

const StudentPerformanceSpiderChart = () => {
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [selectedChapter, setSelectedChapter] = useState(null);
  const [chartOptions, setChartOptions] = useState(null);
  const [chartTitle, setChartTitle] = useState(null);
  const [chartSubTitle, setChartSubTitle] = useState(null);

  useEffect(() => {
    const options = getPerformanceOptions(
      studentPerformanceData,
      selectedSubject,
      selectedChapter
    );
    setChartOptions(options);
  }, [selectedSubject, selectedChapter]);

  const handleDrilldown = (name) => {
    if (!selectedSubject) setSelectedSubject(name);
    else if (!selectedChapter) setSelectedChapter(name);
  };

  const handleBack = () => {
    if (selectedChapter) setSelectedChapter(null);
    else setSelectedSubject(null);
  };

  const getPerformanceOptions = (stats, selectedSubject, selectedChapter) => {
    if (!stats) return null;

    let dataSeries;
    let categories;

    if (selectedSubject && selectedChapter) {
      const subject = stats.find((sub) => sub.subject === selectedSubject);
      const chapter = subject.stats.find(
        (chap) => chap.chapter === selectedChapter
      );

      dataSeries = chapter.topics.map((topic) => ({
        name: topic.topic,
        y: topic.score,
      }));
      setChartTitle(`Topics in ${selectedChapter}`);
      setChartSubTitle("List of All Topics");
      categories = chapter.topics.map((topic) => topic.topic);
    } else if (selectedSubject) {
      const subject = stats.find((sub) => sub.subject === selectedSubject);

      dataSeries = subject.stats.map((chap) => ({
        name: chap.chapter,
        y:
          chap.topics.reduce((acc, topic) => acc + topic.score, 0) /
          chap.topics.length,
        drilldown: chap.chapter,
      }));
      setChartTitle(`${selectedSubject} Performance by Chapter`);
      setChartSubTitle("Click on the Chapter to view Topics");
      categories = subject.stats.map((chap) => chap.chapter);
    } else {
      dataSeries = stats.map((subject) => ({
        name: subject.subject,
        y:
          subject.stats
            .flatMap((chap) => chap.topics)
            .reduce((acc, topic) => acc + topic.score, 0) /
          subject.stats.flatMap((chap) => chap.topics).length,
        drilldown: subject.subject,
      }));
      setChartTitle("Overall Subject Performance");
      setChartSubTitle("Click on the Subject to view Chapters");
      categories = stats.map((subject) => subject.subject);
    }

    return {
      chart: {
        type: "line",
        polar: true,
        height: "380px",
        backgroundColor: {
          linearGradient: { x1: 0, y1: 0, x2: 1, y2: 1 },
          stops: [
            [0, "#00879A"],
            [1, "#00C6E1"],
          ],
        },
      },
      title: { text: null },
      pane: { size: "80%" },
      legend: { enabled: false },
      xAxis: {
        categories,
        tickmarkPlacement: "on",
        lineWidth: 0,
        labels: {
          useHTML: true,
          formatter: function () {
            const labelName = this.value;
            return `<span class="student-clickable-label">${labelName}</span>`;
          },
        },
      },
      yAxis: {
        min: 0,
        max: 100,
        labels: {
          useHTML: true,
          formatter: function () {
            const labelName = this.value;
            return `<span class="student-clickable-label">${labelName}</span>`;
          },
        },
      },
      series: [
        {
          name: chartTitle,
          data: dataSeries,
          pointPlacement: "on",
          colorByPoint: true,
        },
      ],
      tooltip: {
        shared: true,
        pointFormat: "{point.name}: <b>{point.y:.2f}%</b>",
      },
      plotOptions: {
        series: {
          point: {
            events: {
              click: function (event) {
                if (event.point.drilldown) handleDrilldown(event.point.name);
              },
            },
          },
        },
      },
      credits: { enabled: false },
    };
  };

  useEffect(() => {
    const handleLabelClick = (event) => {
      if (event.target.classList.contains("student-clickable-label")) {
        const name = event.target.textContent;
        handleDrilldown(name);
      }
    };

    // Add event listener only when labels are clickable
    if (!(selectedSubject && selectedChapter)) {
      document.addEventListener("click", handleLabelClick);
    }

    // Cleanup function to remove event listener when component unmounts or conditions change
    return () => {
      document.removeEventListener("click", handleLabelClick);
    };
  }, [selectedSubject, selectedChapter]); // This useEffect will run when selectedSubject or selectedChapter changes

  return (
    <MDBox
      sx={{
        position: "relative",
        padding: "20px",
        borderRadius: "12px",
        backgroundColor: "#fff",
        boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
        overflow: "visible",
      }}
    >
      {(selectedSubject || selectedChapter) && (
        <MDBox
          sx={{
            position: "absolute",
            top: -10,
            left: 20,
            zIndex: 50,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            width: "93%",
            mt: 2,
            px: 1,
          }}
        >
          <MDBox
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              cursor: "pointer",
            }}
            onClick={handleBack}
          >
            <ArrowBackIcon color="light" fontSize="medium" />
            <MDTypography variant="h6" color="light">
              Back
            </MDTypography>
          </MDBox>
          <MDBox
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              cursor: "pointer",
            }}
            onClick={() => {
              if (selectedSubject) setSelectedSubject(null);
              if (selectedChapter) setSelectedChapter(null);
            }}
          >
            <RestartAltIcon color="light" fontSize="medium" />
            <MDTypography variant="h6" color="light">
              Reset
            </MDTypography>
          </MDBox>
        </MDBox>
      )}
      <MDBox
        sx={{
          position: "absolute",
          left: 15,
          bottom: 90,
          width: "95%",
          zIndex: 10,
          borderRadius: "12px !important",
          overflow: "hidden",
        }}
      >
        {chartOptions ? (
          <HighchartsReact highcharts={Highcharts} options={chartOptions} />
        ) : (
          <Loader />
        )}
      </MDBox>
      <MDBox
        sx={{
          height: "400px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "flex-end",
        }}
      >
        <MDBox pt={2} pb={1} px={1}>
          <MDTypography variant="h6" fontWeight="medium">
            {chartTitle}
          </MDTypography>
          <MDTypography
            component="div"
            variant="button"
            color="text"
            fontWeight="light"
          >
            {chartSubTitle}
          </MDTypography>
        </MDBox>
      </MDBox>
    </MDBox>
  );
};

export default StudentPerformanceSpiderChart;
