import Highcharts from 'highcharts';
import exporting from 'highcharts/modules/exporting';

exporting(Highcharts);

export const generateChartImage = (chartOptions) => {
  return new Promise((resolve, reject) => {
    try {
      const renderDiv = document.createElement('div');

      const options = {
        ...chartOptions,
        chart: {
          ...chartOptions.chart,
          renderTo: renderDiv,
        },
      };

      const chart = Highcharts.chart(options);

      const svg = chart.getSVG();
      const svgData = new Blob([svg], { type: 'image/svg+xml;charset=utf-8' });
      const DOMURL = window.URL || window.webkitURL || window;
      const url = DOMURL.createObjectURL(svgData);

      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        const imgData = canvas.toDataURL('image/png');
        DOMURL.revokeObjectURL(url);
        resolve(imgData);
      };
      img.onerror = (err) => {
        DOMURL.revokeObjectURL(url);
        reject(err);
      };
      img.src = url;
    } catch (err) {
      reject(err);
    }
  });
};
