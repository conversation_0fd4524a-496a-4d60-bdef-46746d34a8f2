import React from "react";
import { styled } from "@mui/material/styles";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import StepConnector, {
  stepConnectorClasses,
} from "@mui/material/StepConnector";
import PropTypes from "prop-types";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import MDBox from "components/atoms/MDBox";
import PostAddIcon from "@mui/icons-material/PostAdd";
import AutoAwesomeIcon from "@mui/icons-material/AutoAwesome";
import QuestionAnswerIcon from "@mui/icons-material/QuestionAnswer";

const ColorlibConnector = styled(StepConnector)(() => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 18,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage:
        "linear-gradient(95deg, #00879A 0%, #00A6B0 50%, #00C2C9 100%)",
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage:
        "linear-gradient(95deg, #00879A 0%, #00A6B0 50%, #00C2C9 100%)",
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 3,
    border: 0,
    backgroundColor: "#eaeaf0",
    borderRadius: 1,
  },
}));

const ColorlibStepIconRoot = styled("div")(({ ownerState }) => ({
  backgroundColor: "#ccc",
  zIndex: 1,
  color: "#fff",
  width: 40,
  height: 40,
  display: "flex",
  borderRadius: "50%",
  justifyContent: "center",
  alignItems: "center",
  ...(ownerState.active && {
    backgroundImage:
      "linear-gradient(95deg, #00879A 0%, #00A6B0 50%, #00C2C9 100%)",
    boxShadow: "0 4px 10px 0 rgba(0,0,0,.25)",
  }),
  ...(ownerState.completed && {
    backgroundImage:
      "linear-gradient(95deg, #4CAF50 0%, #66BB6A 50%, #81C784 100%)",
    boxShadow: "0 4px 10px 0 rgba(0,0,0,.25)",
  }),
}));

function ColorlibStepIcon(props) {
  const { active, completed } = props;

  const icons = {
    1: <PostAddIcon />,
    2: <QuestionAnswerIcon />,
    3: <AutoAwesomeIcon />,
  };

  return (
    <ColorlibStepIconRoot
      ownerState={{ completed, active }}
      sx={{
        mb: -0.5,
      }}
    >
      {icons[String(props.icon)] || props.icon}
    </ColorlibStepIconRoot>
  );
}

ColorlibStepIcon.propTypes = {
  active: PropTypes.bool,
  completed: PropTypes.bool,
  icon: PropTypes.node,
};

const theme = createTheme({
  components: {
    MuiStepper: {
      styleOverrides: {
        root: {
          backgroundColor: "inherit",
          background: "inherit",
        },
      },
    },
  },
});

export default function CustomizedSteppers({
  activeStep,
  steps,
  formType,
}) {
  let displayedSteps;
  let displayedActiveStep;

  switch (formType) {
    case "add":
      displayedSteps = ["Assignment Form", "Rubric", "Topics"];
      displayedActiveStep = activeStep === 0 ? 0 : activeStep === 1 ? 1 : 2;

      break;

    case "view":
      displayedSteps = ["Assignment Form", "Topics"];
      displayedActiveStep = activeStep === 0 ? 0 : 1;
      break;

    case "edit":
      displayedSteps = ["Assignment Form"];
      displayedActiveStep = 0;
      break;

    default:
      displayedSteps = steps;
      displayedActiveStep = activeStep;
      break;
  }

  return (
    <ThemeProvider theme={theme}>
      <MDBox
        sx={{
          width: "100%",
          p: 2,
        }}
      >
        <Stepper
          alternativeLabel
          activeStep={displayedActiveStep}
          connector={<ColorlibConnector />}
        >
          {displayedSteps.map((label, index) => (
            <Step key={label}>
              <StepLabel
                StepIconComponent={(props) => (
                  <ColorlibStepIcon {...props} icon={index + 1} />
                )}
              >
                {label}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      </MDBox>
    </ThemeProvider>
  );
}
