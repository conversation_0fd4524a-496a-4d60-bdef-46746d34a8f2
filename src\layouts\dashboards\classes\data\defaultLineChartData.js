/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/
const defaultLineChartData = (data, colorMap) => {
  const { months, data: subjectData } = data;
  const datasets = [];

  for (let i = 0; subjectData != null && i < subjectData.length; i++) {
    const subject = subjectData[i];
    datasets.push({
      label: subject.subjects,
      data: subject.assessments,
    });
  }

  const tempData = {
    labels: months,
    datasets: datasets,
  };
  tempData.datasets.forEach((dataset) => {
    dataset.color = colorMap[dataset.label] || "defaultColor";
  });
  return tempData;
};

export default defaultLineChartData;
