import React, { useState, useEffect } from "react";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import { IconButton } from "@mui/material";

const FloatingArrow = ({ scrollToBottom, scrollToTop }) => {
  const [showIcon, setShowIcon] = useState(false);
  const [iconDirection, setIconDirection] = useState("down");

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;

      if (scrollHeight > clientHeight) {
        setShowIcon(true);
      } else {
        setShowIcon(false);
      }

      if (scrollTop + clientHeight >= scrollHeight - 50) {
        setIconDirection("up");
      } else if (scrollTop < 500) {
        setIconDirection("down");
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleClick = () => {
    if (iconDirection === "down") {
      scrollToBottom();
    } else {
      scrollToTop();
    }
  };

  return (
    showIcon && (
      <IconButton
        onClick={handleClick}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "fixed",
          right: 5,
          bottom: 119,
          zIndex: 999,
          width: "2.5rem",
          height: "2.5rem",
          backgroundColor: "#383838",
          borderRadius: "8px",
          boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)",
          "&:hover": {
            backgroundColor: "#000",
          },
        }}
      >
        {iconDirection === "down" ? (
          <ArrowDownwardIcon sx={{ color: "#fff", fontSize: 30 }} />
        ) : (
          <ArrowUpwardIcon sx={{ color: "#fff", fontSize: 30 }} />
        )}
      </IconButton>
    )
  );
};

export default FloatingArrow;
