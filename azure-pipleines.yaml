trigger:
  - main

variables:
  - group: EddyOwl-DigitalOcean

stages:
  - stage: BuildDev
    jobs:
      - deployment: "BuildAndPush"
        displayName: Build and Push Docker Image
        pool:
          vmImage: "ubuntu-latest"
        environment: Dev
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                  persistCredentials: true
                  clean: true
                  fetchDepth: 0
                - task: DownloadSecureFile@1
                  name: DownloadEnv
                  inputs:
                    secureFile: core_env.dev
                - task: DownloadSecureFile@1
                  name: DownloadSentryToken
                  inputs:
                    secureFile: sentryclirc
                - script: |
                    mkdir -p $(Build.SourcesDirectory)/
                    cp $(Agent.TempDirectory)/core_env.dev $(Build.SourcesDirectory)/.env
                    cp $(Agent.TempDirectory)/sentryclirc $(Build.SourcesDirectory)/.sentryclirc
                  displayName: Copy ENV
                - task: DockerInstaller@0
                  inputs:
                    dockerVersion: "19.03.12"
                - script: |
                    export DOCKER_BUILDKIT=0
                    export COMPOSE_DOCKER_CLI_BUILD=0
                    echo $(dockerRegistryPassword) | docker login -u $(dockerRegistryUsername) --password-stdin $(dockerRegistryUrl) 
                    docker build -t $(dockerRegistryUrl)/$(frontEndImageName)-dev:$(Build.BuildId) .
                    docker push $(dockerRegistryUrl)/$(frontEndImageName)-dev:$(Build.BuildId)
                  displayName: Build and Push Docker Image
  - stage: DeployDev
    dependsOn: BuildDev
    jobs:
      - deployment: DeployToDev
        displayName: Deploy to Development
        pool:
          vmImage: "ubuntu-latest"
        environment: Dev
        strategy:
          runOnce:
            deploy:
              steps:
                - task: DownloadSecureFile@1
                  name: DownloadSSHKey
                  inputs:
                    secureFile: $(sshKeyFile)
                - script: |
                    mkdir -p ~/.ssh
                    cp $(Agent.TempDirectory)/$(sshKeyFile) ~/.ssh/$(sshKeyFile)
                    chmod 700 ~/.ssh
                    chmod 600 ~/.ssh/$(sshKeyFile)
                    ssh -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no $(sshUser)@$(coreDropletIP) '
                      echo $(dockerRegistryPassword) | docker login -u $(dockerRegistryUsername) --password-stdin $(dockerRegistryUrl) &&
                      docker pull $(dockerRegistryUrl)/$(frontEndImageName)-dev:$(Build.BuildId) &&
                      docker stop dev_container || true &&
                      docker rm dev_container || true &&
                      docker run -d --name dev_container -p 3001:3000 $(dockerRegistryUrl)/$(frontEndImageName)-dev:$(Build.BuildId)'
                      docker system prune -af
                  displayName: Deploy Docker Image to Development
  - stage: BuildProd
    jobs:
      - deployment: "BuildAndPush"
        displayName: Build and Push Docker Image
        pool:
          vmImage: "ubuntu-latest"
        environment: Prod
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                  persistCredentials: true
                  clean: true
                  fetchDepth: 0
                - task: DownloadSecureFile@1
                  name: DownloadEnv
                  inputs:
                    secureFile: core_env.prod
                - task: DownloadSecureFile@1
                  name: DownloadSentryToken
                  inputs:
                    secureFile: sentryclirc
                - script: |
                    mkdir -p $(Build.SourcesDirectory)/
                    cp $(Agent.TempDirectory)/core_env.prod $(Build.SourcesDirectory)/.env
                    cp $(Agent.TempDirectory)/sentryclirc $(Build.SourcesDirectory)/.sentryclirc
                  displayName: Copy secrets.json to source directory
                - task: DockerInstaller@0
                  inputs:
                    dockerVersion: "19.03.12"
                - script: |
                    export DOCKER_BUILDKIT=0
                    export COMPOSE_DOCKER_CLI_BUILD=0
                    echo $(dockerRegistryPassword) | docker login -u $(dockerRegistryUsername) --password-stdin $(dockerRegistryUrl) 
                    docker build -t $(dockerRegistryUrl)/$(frontEndImageName)-prod:$(Build.BuildId) .
                    docker push $(dockerRegistryUrl)/$(frontEndImageName)-prod:$(Build.BuildId)
                  displayName: Build and Push Docker Image
  - stage: DeployProd
    dependsOn: BuildProd
    jobs:
      - deployment: DeployToProd
        displayName: Deploy to Production
        pool:
          vmImage: "ubuntu-latest"
        environment: Prod
        strategy:
          runOnce:
            deploy:
              steps:
                - task: DownloadSecureFile@1
                  name: DownloadSSHKey
                  inputs:
                    secureFile: $(sshKeyFile)
                - script: |
                    mkdir -p ~/.ssh
                    cp $(Agent.TempDirectory)/$(sshKeyFile) ~/.ssh/$(sshKeyFile)
                    chmod 700 ~/.ssh
                    chmod 600 ~/.ssh/$(sshKeyFile)
                    ssh -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no $(sshUser)@$(coreDropletIP) '
                    echo $(dockerRegistryPassword) | docker login -u $(dockerRegistryUsername) --password-stdin $(dockerRegistryUrl) &&
                      docker pull $(dockerRegistryUrl)/$(frontEndImageName)-prod:$(Build.BuildId) &&
                      docker stop prod_container || true &&
                      docker rm prod_container || true &&
                      docker run -d --name prod_container -p 3000:3000 $(dockerRegistryUrl)/$(frontEndImageName)-prod:$(Build.BuildId)'
                      docker system prune -af
                  displayName: Deploy Docker Image to Production
