import React, { useRef, useEffect } from "react";
import html2pdf from "html2pdf.js";
import "./PdfGenerator.css";
import logo from "../../../assets/images/logo-eddyowl.png";
import { toast } from "react-toastify";
import PerformanceChartsWrapper from "components/molecules/PerformanceChartsWrapper/PerformanceChartsWrapper";
import renderOutOfScore from "utils/helperFunctions/renderOutOfScore";
import HandwrittenLayout from "components/organisms/viewSubmission/HandwrittenLayout/HandwrittenLayout";

function PdfGenerator({
  triggerDownload,
  data,
  onRenderComplete,
  isInsidePdf,
}) {
  const printRef = useRef();
  const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  const waitSleep = async () => {
    await sleep(1000);
  };

  useEffect(() => {
    if (triggerDownload) {
      waitSleep().then(() => generatePDF());
    }
  }, [triggerDownload]);

  const generatePDF = () => {
    const element = printRef.current;

    const opt = {
      margin: [0.2, 0.5, 0.2, 0.5], // top, right, bottom, left
      filename: `${data.name}.pdf`,
      image: { type: "jpeg", quality: 0.98 },
      html2canvas: { scale: 2, useCORS: true },
      jsPDF: { unit: "in", format: "letter", orientation: "portrait" },
      pagebreak: { mode: ["avoid-all", "css", "legacy"] },
    };

    html2pdf()
      .from(element)
      .set(opt)
      .save()
      .then(() => {
        if (onRenderComplete) {
          onRenderComplete();
          toast.success("PDF downloaded successfully");
        }
      });
  };

  return (
    <div
      ref={printRef}
      style={{
        padding: "10px",
        fontSize: "18px",
        fontFamily: "sans-serif",
        backgroundColor: "white",
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          marginBottom: "30px",
        }}
      >
        <img src={logo} width="200px" alt="Company Logo" />
        <p>
          <b>Student ID:</b> {data?.studentId ?? "NA"}
        </p>
      </div>
      <h2
        style={{
          textAlign: "center",
          fontWeight: "600",
          fontSize: "30px",
          marginBottom: "30px",
        }}
      >
        Assignment Report
      </h2>

      <h3
        style={{
          marginBottom: "20px",
          marginLeft: "5px",
        }}
      >
        <b>Test Name:</b> {data?.testName ?? "NA"}
      </h3>
      <hr className="divider-small" />

      <table
        style={{ width: "100%", borderCollapse: "collapse", marginTop: "20px" }}
      >
        <tr>
          <td style={{ border: "1px solid black", padding: "10px" }}>
            <b>Student Name:</b> {data?.name ?? "NA"}
          </td>
          <td style={{ border: "1px solid black", padding: "10px" }}>
            <b>Grade:</b> {data?.class ?? "NA"}
          </td>
        </tr>
        <tr>
          <td style={{ border: "1px solid black", padding: "10px" }}>
            <b>Section:</b> {data?.section ?? "NA"}
          </td>
          <td style={{ border: "1px solid black", padding: "10px" }}>
            <b>Roll Number:</b> {data?.rollNumber ?? "NA"}
          </td>
        </tr>
        <tr>
          <td style={{ border: "1px solid black", padding: "10px" }}>
            <b>Total Score:</b> {data?.totalScore ?? "NA"}
          </td>
          <td style={{ border: "1px solid black", padding: "10px" }}>
            <b>Achieved Score:</b> {data?.totalAchievedScore ?? 0}
          </td>
        </tr>
      </table>
      <hr
        style={{
          marginTop: "50px",
          marginBottom: "10px",
          height: "2px",
          borderBottom: "2px solid black",
        }}
      />
      {data?.StudentResponseList?.length > 0 &&
        data.StudentResponseList.map(
          (item, index) =>
            item?.question && (
              <React.Fragment key={index}>
                <div
                  className="avoid-page-break"
                  style={{
                    display: "flex",
                    marginBottom: "10px",
                    marginLeft: "auto",
                    marginRight: "10px",
                    width: "90px",
                    whiteSpace: "nowrap",
                  }}
                >
                  {renderOutOfScore(item?.score, item?.questionScore, true)}
                </div>

                <div
                  className="avoid-page-break"
                  style={{
                    display: "flex",
                    alignItems: "start",
                    gap: 2,
                  }}
                >
                  <p style={{ marginRight: "5px" }}>
                    <b>{index + 1}.</b>
                  </p>
                  <p>{item?.question ?? "NA"}</p>
                </div>

                <div className="avoid-page-break">
                  <p style={{ marginTop: "20px" }}>
                    <b>Student Response</b>
                  </p>
                  <p style={{ marginTop: "10px" }}>
                    <HandwrittenLayout
                      content={item?.studentResponse ?? "NA"}
                      isInsidePdf={isInsidePdf}
                    />
                  </p>
                </div>
                <div className="avoid-page-break">
                  <p style={{ marginTop: "20px" }}>
                    <b>Feedback</b>
                  </p>
                  <p style={{ marginTop: "20px" }}>
                    <HandwrittenLayout
                      content={item?.feedback ?? "NA"}
                      isInsidePdf={isInsidePdf}
                    />
                  </p>
                </div>
                <hr
                  style={{
                    marginTop: "30px",
                    marginBottom: "20px",
                    height: "2px",
                    borderBottom: "2px solid black",
                  }}
                />
              </React.Fragment>
            )
        )}

      {/* Performance charts */}

      <PerformanceChartsWrapper
        data={data.performance}
        onRenderComplete={onRenderComplete}
      />
    </div>
  );
}

export default PdfGenerator;
