import React, { useContext, useEffect, useState } from "react";
import { Grid } from "@mui/material";
import { ApiServiceContext } from "context";
import ReportsLineChart from "examples/Charts/LineCharts/ReportsLineChart";
import reportsLineChartData from "../../data/reportsLineChartData";
import { toast } from "react-toastify";
import PopOverChartSkeleton from "../../SkeletonLoaders/PopOverChartSkeleton";

const ReportsLineChartWrapper = () => {
  const { apiService } = useContext(ApiServiceContext);

  const [monthlyAssessments, setMonthlyAssessments] = useState(null);

  useEffect(() => {
    if (monthlyAssessments) return;
    apiService
      .getMonthlyAssessmentsForInstitute()
      .then((data) => setMonthlyAssessments(data))
      .catch(() => {
        return toast.error("Error loading monthly assessments");
      });
  }, []);
  return (
    <Grid item xs={12} md={6}>
      {monthlyAssessments ? (
        <ReportsLineChart
          color="dark"
          title="Monthly Graded Assignments"
          description="Previous Months Analysis"
          chart={reportsLineChartData(
            monthlyAssessments?.months,
            monthlyAssessments?.assessments
          )}
        />
      ) : (
        <PopOverChartSkeleton />
      )}
    </Grid>
  );
};

export default ReportsLineChartWrapper;

