import React, { useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import MDBox from "components/atoms/MDBox";
import { ApiServiceContext } from "context";
import { authenticateUser } from "utils/helperFunctions/authenticateUser";
import InstituteForm from "components/organisms/InstituteForm/InstituteForm";
import InstructorsTable from "components/organisms/InstructorsTable/InstructorsTable";
import { Card } from "@mui/material";

function GeneralSettings() {
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);
  const navigate = useNavigate();
  const authUser = authenticateUser(apiService, loading, handleLogin);

  useEffect(() => {
    authUser();
  }, [authUser]);

  useEffect(() => {
    const checkUserRoles = async () => {
      if (!apiService?.instituteId) {
        try {
          const userRoles = await apiService.getUserRoles();
          if (userRoles.roles && userRoles.roles.length > 0) {
            navigate("/switch-institute", { replace: true });
          } else {
            navigate("/create-new-institute", { replace: true });
          }
        } catch (error) {
          
          navigate("/create-new-institute", { replace: true });
        }
      }
    };
    checkUserRoles();
  }, [apiService, navigate]);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <MDBox py={2} mb={3}>
        <Card>
          <MDBox p={1}>
            <InstituteForm />

            <InstructorsTable />
          </MDBox>
        </Card>
      </MDBox>
    </DashboardLayout>
  );
}

export default GeneralSettings;
