/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

const studentBySectionsData = (data) => {
  return (
    data.sections &&
    data.sections.map((section, index) => ({
      section: ["", section],
      students: data.students[index],
    }))
  );
};

export default studentBySectionsData;
