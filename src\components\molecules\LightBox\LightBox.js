import React from "react";
import Lightbox from "react-18-image-lightbox";
import "react-18-image-lightbox/style.css";

export const LightBox = ({
  previews,
  setIsLightboxOpen,
  setPhotoIndex,
  photoIndex,
}) => {
  return (
    <Lightbox
      mainSrc={previews[photoIndex]}
      nextSrc={previews[(photoIndex + 1) % previews.length]}
      prevSrc={previews[(photoIndex + previews.length - 1) % previews.length]}
      onCloseRequest={() => setIsLightboxOpen(false)}
      onMovePrevRequest={() =>
        setPhotoIndex((photoIndex + previews.length - 1) % previews.length)
      }
      onMoveNextRequest={() =>
        setPhotoIndex((photoIndex + 1) % previews.length)
      }
    />
  );
};
