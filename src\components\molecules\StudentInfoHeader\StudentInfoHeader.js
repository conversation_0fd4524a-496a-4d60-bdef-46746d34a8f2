import React from 'react';
import PropTypes from 'prop-types';
import MDBox from 'components/atoms/MDBox';
import MDTypography from 'components/atoms/MDTypography';

const StudentInfoHeader = ({ studentName, grade, section, rollNumber }) => (
  <MDBox
    display="flex"
    flexDirection={{ xs: "column", sm: "row" }}
    alignItems="start"
    justifyContent={{ xs: "space-between", sm: "initial" }}
    p={1}
    gap={{ xs: 1, sm: 3 }}
    ml={1}
    mb={{ xs: 8, lg: 2 }}
    mt={-1}
  >
    <MDBox display="flex" alignItems="center" gap={1}>
      <MDTypography
        variant="h5"
        fontWeight="regular"
        sx={{ textWrap: "nowrap" }}
      >
        Student:
      </MDTypography>
      <MDTypography
        variant="h5"
        fontWeight="medium"
        sx={{ textWrap: "word-break" }}
      >
        {studentName}
      </MDTypography>
    </MDBox>
    <MDBox display="flex" alignItems="center" gap={1}>
      <MDTypography
        variant="h5"
        fontWeight="regular"
        sx={{ textWrap: "nowrap" }}
      >
        Grade:
      </MDTypography>
      <MDTypography
        variant="h5"
        fontWeight="medium"
        sx={{ textWrap: "word-break" }}
      >
        {`${grade} (${section})`}
      </MDTypography>
    </MDBox>
    <MDBox display="flex" alignItems="center" gap={1}>
      <MDTypography
        variant="h5"
        fontWeight="regular"
        sx={{ textWrap: "nowrap" }}
      >
        Roll No:
      </MDTypography>
      <MDTypography
        variant="h5"
        fontWeight="medium"
        sx={{ textWrap: "word-break" }}
      >
        {rollNumber}
      </MDTypography>
    </MDBox>
  </MDBox>
);

export default StudentInfoHeader;

