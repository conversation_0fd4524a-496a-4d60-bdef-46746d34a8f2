const studentReportData = {
  studentId: "009A001",
  name: "<PERSON>",
  class: "10",
  section: "A",
  rollNumber: "21",
  period: "10/01/2024 - 10/03/2024",
  stats: [
    {
      subject: "English",
      studentAverageScore: 80,
      classAverageScore: 70,
      tests: [
        {
          name: "English Unit Test",
          achievedScore: 8,
          classAverageScore: 7,
          chapters: [
            {
              chapter: "Grammar",
              topics: [
                { topic: "Nouns", score: 7 },
                { topic: "Verbs", score: 8 },
                { topic: "Tenses", score: 6 },
              ],
            },
            {
              chapter: "Literature",
              topics: [
                { topic: "Poetry", score: 8 },
                { topic: "Short Stories", score: 6 },
                { topic: "Novels", score: 9 },
              ],
            },
            {
              chapter: "Essay Writing",
              topics: [
                { topic: "Formal Essay", score: 8 },
                { topic: "Descriptive Essay", score: 7 },
                { topic: "Narrative Essay", score: 10 },
              ],
            },
          ],
        },
        {
          name: "English Midterm Exam",
          achievedScore: 9,
          classAverageScore: 8,
          chapters: [
            {
              chapter: "Grammar",
              topics: [
                { topic: "Nouns", score: 8 },
                { topic: "Pronouns", score: 7 },
                { topic: "Adjectives", score: 9 },
              ],
            },
            {
              chapter: "Comprehension",
              topics: [
                { topic: "Passage Analysis", score: 7 },
                { topic: "Critical Reading", score: 6 },
              ],
            },
            {
              chapter: "Vocabulary",
              topics: [
                { topic: "Synonyms", score: 8 },
                { topic: "Antonyms", score: 7 },
                { topic: "Idioms", score: 9 },
              ],
            },
            {
              chapter: "Literature",
              topics: [
                { topic: "Drama", score: 8 },
                { topic: "Novels", score: 7 },
              ],
            },
          ],
        },
      ],
    },
    // New Mathematics Stats Object
    {
      subject: "Mathematics",
      studentAverageScore: 85,
      classAverageScore: 75,
      tests: [
        {
          name: "Mathematics Unit Test",
          achievedScore: 9,
          classAverageScore: 7.5,
          chapters: [
            {
              chapter: "Algebra",
              topics: [
                { topic: "Linear Equations", score: 9 },
                { topic: "Quadratic Equations", score: 8.5 },
                { topic: "Functions", score: 9.5 },
              ],
            },
            {
              chapter: "Geometry",
              topics: [
                { topic: "Triangles", score: 8.5 },
                { topic: "Circles", score: 9 },
                { topic: "Area and Volume", score: 9.5 },
              ],
            },
          ],
        },
        {
          name: "Mathematics Midterm Exam",
          achievedScore: 10,
          classAverageScore: 8.5,
          chapters: [
            {
              chapter:"Statistics",
              topics:[
                  {topic:"Mean",score :9},
                  {topic:"Median",score :10},
                  {topic:"Mode",score :8}
               ]
            }
          ]
        }
      ],
    }
  ],
};

export default studentReportData;