import React, { useContext, useEffect, useState } from "react";
import Grid from "@mui/material/Grid";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import Icon from "@mui/material/Icon";
import Tooltip from "@mui/material/Tooltip";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import DefaultLineChart from "examples/Charts/LineCharts/DefaultLineChart";
import ChartsSkeleton from "../Loaders/ChartsSkeleton";
import defaultLineChartData from "../../data/defaultLineChartData";

const MonthlyAssessmentsChartWrapper = ({ grade }) => {
  const { apiService } = useContext(ApiServiceContext);
  const [monthlyData, setMonthlyData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [colorMap, setColorMap] = useState(null);

  const colors = [
    "primary",
    "secondary",
    "info",
    "success",
    "warning",
    "error",
    "light",
    "dark",
  ];

  const processColorMap = (subjects) => {
    const subjectColorMap = {};
    subjects.forEach((subject, index) => {
      subjectColorMap[subject] = colors[index % colors.length];
    });
    return subjectColorMap;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await apiService.getClassMonthlyAssessmentsBySubject(grade);
        const subjects = data.data?.map(item => item.subjects) || [];
        setColorMap(processColorMap(subjects));
        setMonthlyData(data);
      } catch (err) {
        toast.error("Error loading monthly assessment data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [apiService, grade]);

  if (loading) return (
    <Grid item xs={12} md={6}>
      <ChartsSkeleton />
    </Grid>
  );

  if (!monthlyData) return null;

  return (
    <Grid item xs={12} md={6}>
      <DefaultLineChart
        title="Monthly Total Assignments By Subject"
        description={
          <MDBox display="flex" justifyContent="space-between">
            <MDBox mt={-4} mr={-1} position="absolute" right="1.5rem">
              <Tooltip
                title="See the total assignments graded and ungraded by subject"
                placement="left"
                arrow
              >
                <MDButton
                  variant="outlined"
                  color="secondary"
                  size="small"
                  circular
                  iconOnly
                >
                  <Icon>priority_high</Icon>
                </MDButton>
              </Tooltip>
            </MDBox>
          </MDBox>
        }
        chart={defaultLineChartData(monthlyData, colorMap)}
      />
    </Grid>
  );
};

export default MonthlyAssessmentsChartWrapper;
