/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// @mui material components
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";

// Data
import HorizontalBarChart from "examples/Charts/BarCharts/HorizontalBarChart";

function AssessmentByClassHorizontalBar({ retreivedData }) {
  return (
    <Card sx={{ width: "100%" }}>
      <MDBox display="flex">
        <MDBox
          display="flex"
          justifyContent="center"
          alignItems="center"
          width="4rem"
          height="4rem"
          variant="gradient"
          bgColor="dark"
          color="white"
          shadow="md"
          borderRadius="xl"
          ml={3}
          mt={-2}
        >
          <Icon fontSize="medium" color="inherit">
            assessment
          </Icon>
        </MDBox>
        <MDBox sx={{ mt: 2, mb: 1, ml: 2 }}>
          <MDTypography variant="h6">Assignments by Grade</MDTypography>
          <MDTypography
            component="div"
            variant="button"
            color="text"
            fontWeight="light"
          >
            Get total assignments for grades
          </MDTypography>
        </MDBox>
      </MDBox>
      <MDBox p={2} pt={0}>
        <Grid container>
          <Grid item xs={12}>
            <HorizontalBarChart chart={retreivedData} height={"26rem"} />
          </Grid>
        </Grid>
      </MDBox>
    </Card>
  );
}

export default AssessmentByClassHorizontalBar;
