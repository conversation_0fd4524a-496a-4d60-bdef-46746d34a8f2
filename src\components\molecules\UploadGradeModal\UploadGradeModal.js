import { Input<PERSON>abel, Tooltip } from "@mui/material";
import Loader from "components/atoms/Loader/Loader";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import { Modal } from "components/atoms/Modal/Modal";
import React, { useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import { ApiServiceContext } from "context";
import { LightBox } from "../LightBox/LightBox";
import { convertPdfToImages } from "utils/helperFunctions/convertPdfToImages";
import { compressAndUploadFiles } from "utils/helperFunctions/compressAndUploadFiles";
import { useParams } from "react-router-dom";
import SUBMISSION_STATUS from "utils/helperFunctions/SUBMISSION_STATUS";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import HourglassTopIcon from "@mui/icons-material/HourglassTop";
import PreviewsDraggableArea from "../CreateTestForm/components/PreviewsDraggableArea/PreviewsDraggableArea";
import CircularProgress from "@mui/material/CircularProgress";
import { truncateText } from "utils/helperFunctions/truncateText";

export const UploadGradeModal = ({
  isGradeModalOpen,
  setIsGradeModalOpen,
  assignmentId: propAssignmentId, // Rename the prop for clarity
  selectedStudentId,
  setSelectedStudentId,
  setAllStudents,
  allStudents,
  isUploadModalLoading,
  setIsUploadModalLoading,
  isFromViewSubmission = false,
  responseFormStatus = null,
  setResponseFormStatus = () => {},
  setTableData = () => {},
  isStudentRole = false,
}) => {
  const { assignmentId } = useParams();
  const ASSIGNMENT_ID = propAssignmentId || assignmentId;
  const [files, setFiles] = useState([]);
  const [previews, setPreviews] = useState([]);
  const [errorFiles, setErrorFiles] = useState([]);
  const [fileUploadLoader, setFileUploadLoader] = useState(false);
  const { apiService } = useContext(ApiServiceContext);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [photoIndex, setPhotoIndex] = useState(0);

  const [studentDetails, setStudentDetails] = useState({});
  const [testDetails, setTestDetails] = useState({});

  const [pdfLoading, setPdfLoading] = useState(false);
  const [error, setError] = useState(null);

  const [isSubmissionProcessing, setIsSubmissionProcessing] = useState(false);

  const handleFileSubmit = async () => {
    if (files.length === 0) {
      toast.error("Please select at least one file.");
      return;
    }
    setFileUploadLoader(true);

    const fileLocationGenerator = (file, index) => {
      const fileExtension = file.name.split(".").pop();
      return `${
        apiService.instituteId
      }/${ASSIGNMENT_ID}/${selectedStudentId}/page-${
        index + 1
      }.${fileExtension}`;
    };

    const { success, tmpFileList, errorFiles } = await compressAndUploadFiles(
      files,
      fileLocationGenerator
    );

    if (!success) {
      toast.error("Error uploading files.");
      setErrorFiles(errorFiles);
      setFileUploadLoader(false);
      setPreviews([]);
      setFiles([]);
      setIsGradeModalOpen(false);
      return;
    }

    try {
      await apiService.uploadStudentSubmission(
        ASSIGNMENT_ID,
        selectedStudentId,
        tmpFileList
      );

      setAllStudents((prevStudents) =>
        prevStudents.map((student) =>
          isStudentRole
            ? student.assignmentId === ASSIGNMENT_ID
            : student.studentId === selectedStudentId
            ? {
                ...student,
                status: SUBMISSION_STATUS.PROCESSING,
              }
            : student
        )
      );

      // Add this condition to update tableData only for student role
      if (isStudentRole) {
        setTableData((prevData) => ({
          ...prevData,
          rows: prevData.rows.map((row) =>
            row.assignmentId === ASSIGNMENT_ID
              ? {
                  ...row,
                  status: SUBMISSION_STATUS.PROCESSING,
                }
              : row
          ),
        }));
      }

      setSelectedStudentId(null);
      toast.success("Files uploaded successfully.");

      if (isFromViewSubmission) {
        setIsSubmissionProcessing(true);

        await new Promise((resolve) => {
          setTimeout(() => {
            resolve();
          }, 500);
        });

        setResponseFormStatus(SUBMISSION_STATUS.PROCESSING);
        setIsSubmissionProcessing(false);
      }
    } catch (err) {
      toast.error("Error processing files.");
    } finally {
      setIsGradeModalOpen(false);
      setFileUploadLoader(false);
      setFiles([]);
      setPreviews([]);
      setPdfLoading(false);
      setError(null);
      setErrorFiles([]);
    }
  };

  const handleRemoveFile = (index) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
    const newPreviews = [...previews];
    newPreviews.splice(index, 1);
    setPreviews(newPreviews);
  };

  const handleFileChange = async (event) => {
    setPdfLoading(true);

    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "application/pdf",
    ];
    const allFiles = [...event.target.files];

    // Step 1: Filter only allowed files
    const validFiles = allFiles.filter((file) =>
      allowedTypes.includes(file.type)
    );
    const newFilesList = [];
    const newPreviews = [];

    // Helper function to handle file reading and updating previews
    const readFile = (file) => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          newPreviews.push(reader.result); // Add preview to the array
          resolve();
        };
        reader.readAsDataURL(file);
      });
    };

    // Process each file
    for (const file of validFiles) {
      if (file.type === "application/pdf") {
        // If PDF, convert to images first, then process images
        const pdfImages = await convertPdfToImages(file);
        for (const imageFile of pdfImages) {
          newFilesList.push(imageFile); // Add converted image files to list
          await readFile(imageFile); // Read the image file as preview
        }
      } else {
        // Non-PDF file, just add it to the list and process preview
        newFilesList.push(file);
        await readFile(file); // Read the non-PDF file as preview
      }
    }

    // Now, update the state with all files and previews
    setFiles((prevFiles) => [...prevFiles, ...newFilesList]);
    setPreviews((prevPreviews) => [...prevPreviews, ...newPreviews]);

    setPdfLoading(false);
  };

  const handleClose = () => {
    setFiles([]);
    setPreviews([]);
    setIsGradeModalOpen(false);
    setPdfLoading(false);
    setIsUploadModalLoading(false);
    setFileUploadLoader(false);
    setError(null);
    setErrorFiles([]);
  };

  const openLightbox = (index) => {
    setPhotoIndex(index);
    setIsLightboxOpen(true);
  };

  const getTestDetails = async () => {
    try {
      const result = await apiService.getAssignment(ASSIGNMENT_ID);
      setTestDetails(result);
      setIsUploadModalLoading(false);
    } catch (err) {
      setError(err);
      toast.error("Error In Loading Test Details");
      setIsUploadModalLoading(false);
      setIsGradeModalOpen(false);
    }
  };

  useEffect(() => {
    if (isFromViewSubmission) return;

    getTestDetails();

    const getStudentId = selectedStudentId;

    const result = allStudents?.find(
      (student) => student?.studentId === getStudentId
    );
    setStudentDetails(result);
  }, [
    selectedStudentId,
    allStudents,
    isGradeModalOpen,
    propAssignmentId,
    assignmentId,
  ]);

  const RenderLayout = ({ children, isFromViewSubmission }) => {
    return isFromViewSubmission ? (
      <>{children}</>
    ) : (
      <Modal
        isOpen={isGradeModalOpen}
        onClose={() => handleClose()}
        headingText="Upload Submission Files"
      >
        {children}
      </Modal>
    );
  };

  const renderCustomAssignmentName = (text, limit = 30) => {
    if (text?.length > limit) {
      return (
        <>
          <Tooltip
            title={text}
            placement="top"
            componentsProps={{
              tooltip: {
                sx: {
                  whiteSpace: "nowrap",
                  maxWidth: "none",
                },
              },
            }}
          >
            {truncateText(text, limit)}
          </Tooltip>
        </>
      );
    } else {
      return text;
    }
  };

  return (
    <>
      {isUploadModalLoading ? (
        <Loader overlay={true} fullScreen={true} />
      ) : (
        <RenderLayout isFromViewSubmission={isFromViewSubmission}>
          {/* Top Details Section */}

          {!isFromViewSubmission && (
            <MDBox
              display="flex"
              flexDirection={{
                xs: "column",
                sm: "row",
              }}
              alignItems="flex-start"
              justifyContent="space-between"
              gap={{
                xs: 1,
                sm: 0,
              }}
              p={1}
              mt={1}
              px={{ xs: 0, md: "5vw", lg: "10vw", xl: 0 }}
            >
              {/* Student Details */}
              <MDBox>
                <MDBox display="flex" alignItems="start" gap={2}>
                  <MDTypography
                    variant="h6"
                    fontWeight="regular"
                    sx={{ textWrap: "nowrap" }}
                  >
                    Student :
                  </MDTypography>
                  <MDTypography
                    variant="h6"
                    fontWeight="medium"
                    sx={{ textWrap: "word-break" }}
                  >
                    {studentDetails?.studentName}
                  </MDTypography>
                </MDBox>
                <MDBox display="flex" alignItems="start" gap={2}>
                  <MDTypography
                    variant="h6"
                    fontWeight="regular"
                    sx={{ textWrap: "nowrap" }}
                  >
                    Grade :
                  </MDTypography>
                  <MDTypography
                    variant="h6"
                    fontWeight="medium"
                    sx={{ textWrap: "word-break" }}
                  >
                    {`${studentDetails?.class} (${studentDetails?.section})`}
                  </MDTypography>
                </MDBox>
                <MDBox display="flex" alignItems="start" gap={2}>
                  <MDTypography
                    variant="h6"
                    fontWeight="regular"
                    sx={{ textWrap: "nowrap" }}
                  >
                    Roll No :
                  </MDTypography>
                  <MDTypography
                    variant="h6"
                    fontWeight="medium"
                    sx={{ textWrap: "word-break" }}
                  >
                    {studentDetails?.studentRollNumber}
                  </MDTypography>
                </MDBox>
              </MDBox>

              {/* Assignment Details */}
              <MDBox>
                <MDBox display="flex" alignItems="start" gap={2}>
                  <MDTypography
                    variant="h6"
                    fontWeight="regular"
                    sx={{ textWrap: "nowrap" }}
                  >
                    Assignment :
                  </MDTypography>
                  <MDTypography
                    variant="h6"
                    fontWeight="medium"
                    sx={{
                      textWrap: "word-break",
                      cursor: testDetails?.name?.length > 20 && "pointer",
                    }}
                  >
                    {renderCustomAssignmentName(testDetails?.name, 20)}
                  </MDTypography>
                </MDBox>
                <MDBox display="flex" alignItems="start" gap={2}>
                  <MDTypography
                    variant="h6"
                    fontWeight="regular"
                    sx={{ textWrap: "nowrap" }}
                  >
                    Subject :
                  </MDTypography>
                  <MDTypography
                    variant="h6"
                    fontWeight="medium"
                    sx={{ textWrap: "word-break" }}
                  >
                    {testDetails?.subjectName}
                  </MDTypography>
                </MDBox>
              </MDBox>
            </MDBox>
          )}
          {responseFormStatus === SUBMISSION_STATUS.DUE && (
            <MDBox
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              mb={5}
              mt={files.length === 0 ? 8 : 3}
              gap={4}
            >
              <ErrorOutlineIcon
                color="warning"
                sx={{
                  fontSize: { xs: "3rem !important", xl: "4rem !important" },
                }}
              />

              <MDTypography variant="h4" fontWeight="bold">
                Submission Due
              </MDTypography>

              <MDTypography
                variant="h5"
                fontWeight="regular"
                sx={{
                  fontSize: {
                    xs: "1rem",
                    md: "1.3rem",
                  },
                }}
              >
                Click on the button below to choose student's submission files
              </MDTypography>
            </MDBox>
          )}

          {responseFormStatus === SUBMISSION_STATUS.PROCESSING && (
            <MDBox
              mt={8}
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              sx={{ minHeight: "calc(100vh - 35rem)" }}
              gap={4}
            >
              {isSubmissionProcessing ? (
                <MDBox
                  sx={{
                    display: "flex",
                    alighItems: "center",
                    justifyContent: "center",
                    minHeight: "calc(100vh - 25rem)",
                  }}
                >
                  <Loader />
                </MDBox>
              ) : (
                <>
                  <HourglassTopIcon
                    color="info"
                    sx={{
                      fontSize: {
                        xs: "3rem !important",
                        xl: "4rem !important",
                      },
                    }}
                  />
                  <MDTypography variant="h4" fontWeight="bold">
                    Submission is Processing
                  </MDTypography>
                  <MDTypography
                    variant="h5"
                    fontWeight="regular"
                    sx={{
                      fontSize: {
                        xs: "1rem",
                        md: "1.3rem",
                      },
                    }}
                  >
                    Revisit the submission after some time to get updated one
                  </MDTypography>
                </>
              )}
            </MDBox>
          )}

          <MDBox
            mt={3}
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="space-between"
            sx={{ minHeight: isFromViewSubmission && "calc(100vh - 32rem)" }}
          >
            <MDButton
              variant="gradient"
              color="info"
              sx={{ width: "200px", padding: "0", marginBottom: "30px" }}
            >
              <InputLabel
                sx={{
                  width: "200px",
                  padding: "10px",
                  justifyContent: "center",
                }}
                color="primary"
              >
                <input
                  key={files.length - 1}
                  type="file"
                  multiple
                  accept=".jpg,.jpeg,.png,.pdf"
                  onChange={handleFileChange}
                  className="mb-4"
                  class="hidden"
                />
                <MDTypography
                  variant="h6"
                  color="white"
                  fontWeight="regular"
                  px={2}
                  width="100%"
                >
                  {files.length === 0 ? "Choose Files" : "Select More Files"}
                </MDTypography>
              </InputLabel>
            </MDButton>
            {/* Drag and drop area for Previews */}
            <PreviewsDraggableArea
              previews={previews}
              handleRemoveFile={handleRemoveFile}
              pdfLoading={pdfLoading}
              openLightbox={openLightbox}
              setPreviews={setPreviews}
              setFiles={setFiles}
            />
            <MDBox
              className="flex justify-between mt-5"
              width="100%"
              sx={{
                justifyContent: isFromViewSubmission && "flex-end",
              }}
            >
              {!isFromViewSubmission && (
                <MDButton
                  variant="outlined"
                  color="error"
                  onClick={handleClose}
                >
                  Cancel
                </MDButton>
              )}
              <MDButton
                variant="gradient"
                color="dark"
                onClick={handleFileSubmit}
                disabled={files.length === 0 || fileUploadLoader}
                sx={{
                  width: "9rem",
                  display: isFromViewSubmission && files.length === 0 && "none",
                }}
              >
                {fileUploadLoader ? <Loader loaderColor="#fff" /> : "Submit"}
              </MDButton>
            </MDBox>
          </MDBox>
        </RenderLayout>
      )}

      {/* to make screen unclickable when files are submitting */}
      {fileUploadLoader && isFromViewSubmission && (
        <div
          className="fixed left-0 top-0 flex h-screen w-screen items-center justify-center bg-[#00000045]"
          style={{ zIndex: "10000" }}
        >
          <MDBox ml={30}>
            <CircularProgress color="inherit" />
          </MDBox>
        </div>
      )}

      {isLightboxOpen && (
        <LightBox
          previews={previews}
          setIsLightboxOpen={setIsLightboxOpen}
          setPhotoIndex={setPhotoIndex}
          photoIndex={photoIndex}
        />
      )}
    </>
  );
};
