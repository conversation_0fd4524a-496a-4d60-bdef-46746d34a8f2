const studentPerformanceData = [
  {
    subject: "English",
    stats: [
      {
        chapter: "Grammar",
        topics: [
          { topic: "Nouns", score: 78 },
          { topic: "Verbs", score: 85 },
          { topic: "Tenses", score: 72 },
        ],
      },
      {
        chapter: "Literature",
        topics: [
          { topic: "Poetry", score: 88 },
          { topic: "Short Stories", score: 65 },
          { topic: "Novels", score: 91 },
        ],
      },
      {
        chapter: "Essay Writing",
        topics: [
          { topic: "Formal Essay", score: 80 },
          { topic: "Descriptive Essay", score: 67 },
          { topic: "Narrative Essay", score: 90 },
        ],
      },
      {
        chapter: "Comprehension",
        topics: [
          { topic: "Passage Analysis", score: 74 },
          { topic: "Critical Reading", score: 62 },
        ],
      },
      {
        chapter: "Vocabulary",
        topics: [
          { topic: "Synonyms", score: 70 },
          { topic: "Antonyms", score: 68 },
          { topic: "Idioms", score: 83 },
        ],
      },
      // Additional chapters...
    ],
  },
  {
    subject: "Mathematics",
    stats: [
      {
        chapter: "Algebra",
        topics: [
          { topic: "Linear Equations", score: 80 },
          { topic: "Quadratic Equations", score: 68 },
          { topic: "Polynomials", score: 72 },
        ],
      },
      {
        chapter: "Geometry",
        topics: [
          { topic: "Triangles", score: 75 },
          { topic: "Linear Equations", score: 50 },
          { topic: "Circles", score: 60 },
          { topic: "Coordinate Geometry", score: 85 },
        ],
      },
      {
        chapter: "Trigonometry",
        topics: [
          { topic: "Sine and Cosine", score: 78 },
          { topic: "Identities", score: 64 },
          { topic: "Height and Distance", score: 80 },
        ],
      },
      // Additional chapters...
    ],
  },
  {
    subject: "Science",
    stats: [
      {
        chapter: "Physics",
        topics: [
          { topic: "Newton's Laws", score: 85 },
          { topic: "Work and Energy", score: 90 },
          { topic: "Motion", score: 78 },
        ],
      },
      {
        chapter: "Chemistry",
        topics: [
          { topic: "Acids and Bases", score: 75 },
          { topic: "Periodic Table", score: 82 },
          { topic: "Chemical Reactions", score: 87 },
        ],
      },
      {
        chapter: "Biology",
        topics: [
          { topic: "Cell Structure", score: 80 },
          { topic: "Genetics", score: 70 },
          { topic: "Human Anatomy", score: 88 },
        ],
      },
      // Additional chapters...
    ],
  },
  {
    subject: "History",
    stats: [
      {
        chapter: "Ancient History",
        topics: [
          { topic: "Harappan Civilization", score: 72 },
          { topic: "Mesopotamia", score: 65 },
          { topic: "Egyptian Civilization", score: 80 },
        ],
      },
      {
        chapter: "Medieval History",
        topics: [
          { topic: "Mughals", score: 78 },
          { topic: "Feudal System", score: 62 },
        ],
      },
      {
        chapter: "Modern History",
        topics: [
          { topic: "World Wars", score: 82 },
          { topic: "Industrial Revolution", score: 74 },
        ],
      },
      // Additional chapters...
    ],
  },
  {
    subject: "Geography",
    stats: [
      {
        chapter: "Physical Geography",
        topics: [
          { topic: "Mountains", score: 78 },
          { topic: "Rivers", score: 88 },
        ],
      },
      {
        chapter: "Human Geography",
        topics: [
          { topic: "Population", score: 72 },
          { topic: "Urbanization", score: 70 },
        ],
      },
      {
        chapter: "Environmental Geography",
        topics: [
          { topic: "Climate Change", score: 85 },
          { topic: "Pollution", score: 75 },
        ],
      },
      // Additional chapters...
    ],
  },
  {
    subject: "Computer Science",
    stats: [
      {
        chapter: "Programming",
        topics: [
          { topic: "Variables", score: 80 },
          { topic: "Loops", score: 70 },
          { topic: "Functions", score: 88 },
        ],
      },
      {
        chapter: "Data Structures",
        topics: [
          { topic: "Arrays", score: 78 },
          { topic: "Stacks", score: 65 },
          { topic: "Queues", score: 82 },
        ],
      },
      {
        chapter: "Databases",
        topics: [
          { topic: "SQL", score: 90 },
          { topic: "Normalization", score: 72 },
        ],
      },
      // Additional chapters...
    ],
  },
  {
    subject: "Economics",
    stats: [
      {
        chapter: "Microeconomics",
        topics: [
          { topic: "Demand and Supply", score: 85 },
          { topic: "Elasticity", score: 70 },
        ],
      },
      {
        chapter: "Macroeconomics",
        topics: [
          { topic: "GDP", score: 80 },
          { topic: "Inflation", score: 75 },
          { topic: "Unemployment", score: 68 },
        ],
      },
      // Additional chapters...
    ],
  },
  {
    subject: "Civics",
    stats: [
      {
        chapter: "Democracy",
        topics: [
          { topic: "Elections", score: 88 },
          { topic: "Political Parties", score: 72 },
        ],
      },
      {
        chapter: "Government",
        topics: [
          { topic: "Parliament", score: 78 },
          { topic: "Judiciary", score: 80 },
        ],
      },
      // Additional chapters...
    ],
  },
  {
    subject: "Physics",
    stats: [
      {
        chapter: "Mechanics",
        topics: [
          { topic: "Kinematics", score: 85 },
          { topic: "Dynamics", score: 72 },
        ],
      },
      {
        chapter: "Optics",
        topics: [
          { topic: "Reflection", score: 80 },
          { topic: "Refraction", score: 78 },
        ],
      },
      // Additional chapters...
    ],
  },
  {
    subject: "Chemistry",
    stats: [
      {
        chapter: "Organic Chemistry",
        topics: [
          { topic: "Hydrocarbons", score: 88 },
          { topic: "Alcohols", score: 75 },
        ],
      },
      {
        chapter: "Inorganic Chemistry",
        topics: [
          { topic: "Metals", score: 72 },
          { topic: "Non-metals", score: 80 },
        ],
      },
      // Additional chapters...
    ],
  },
];

export default studentPerformanceData;
