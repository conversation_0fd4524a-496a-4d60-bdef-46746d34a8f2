import React, { useState, useContext, useEffect } from "react";
import { <PERSON><PERSON>, Card } from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import TermList from "components/organisms/TermList/TermList";
import TermForm from "components/organisms/TermForm/TermForm";
import Loader from "components/atoms/Loader/Loader";
import { Modal } from "components/atoms/Modal/Modal";
import DeleteTermModal from "components/organisms/DeleteTermModal/DeleteTermModal";
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty";
import isRangeOverlapping from "utils/helperFunctions/isRangeOverlapping";
import { useTermStatus } from "context/useTermStatus";
import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import checkIsCurrentTerm from "utils/helperFunctions/checkIsCurrentTerm";

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

function AcademicTermsSettings() {
  const { apiService } = useContext(ApiServiceContext);
  const { checkTerms, termsLoading, hasCurrentTerm } = useTermStatus();
  const [terms, setTerms] = useState([]);
  const [isTermModalOpen, setIsTermModalOpen] = useState(false);
  const [termFormMode, setTermFormMode] = useState("view");
  const [editingTermIndex, setEditingTermIndex] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deletingTermIndex, setDeletingTermIndex] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deletingTermData, setDeletingTermData] = useState(null);

  useEffect(() => {
    fetchTerms();
  }, []);

  const fetchTerms = async () => {
    try {
      setIsLoading(true);
      const termsData = await apiService.getTerms();
      setTerms(termsData);
    } catch (error) {
      toast.error("Failed to fetch terms");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddNewTermSubmit = async (termData, actions) => {
    const startDate = termData.startDate;
    const endDate = termData.endDate;

    const { hasOverlap, overlappingTermNames } = isRangeOverlapping(
      startDate,
      endDate,
      terms,
      editingTermIndex
    );

    if (hasOverlap) {
      toast.error(
        `The selected date range overlaps with: ${overlappingTermNames.join(
          ", "
        )}`
      );
      actions.setSubmitting(false);
      return;
    }

    try {
      const formattedData = {
        name: termData.name,
        startDate: new Date(startDate).toISOString(),
        endDate: new Date(endDate).toISOString(),
      };

      await apiService.createNewTerm(apiService.instituteId, formattedData);
      toast.success("Term added successfully");

      // Check if the newly added term is a current term
      const isCurrentTerm = checkIsCurrentTerm(startDate, endDate);

      if (isCurrentTerm) {
        // Reload the page if the new term is current
        window.location.reload();
      } else {
        // Otherwise, just refresh the terms list
        await fetchTerms();
      }

      actions.resetForm();
      handleCloseModal();
    } catch (error) {
      toast.error("Failed to save term");
    }
    actions.setSubmitting(false);
  };

  const handleAddMoreTerm = () => {
    setTermFormMode("more");
    setEditingTermIndex(null);
    setIsTermModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsTermModalOpen(false);
    setTermFormMode("view");
    setEditingTermIndex(null);
  };

  const handleDeleteTerm = async () => {
    if (!deletingTermData) return;

    const isCurrentTerm = checkIsCurrentTerm(
      deletingTermData.startDate,
      deletingTermData.endDate
    );

    setIsDeleting(true);
    try {
      await apiService.deleteTerm(deletingTermData.termId);

      // Update terms state by filtering out the deleted term
      setTerms((prevTerms) =>
        prevTerms.filter((_, index) => index !== deletingTermIndex)
      );

      toast.success("Term deleted successfully");

      // If we're deleting the current term, refresh the page
      if (isCurrentTerm) {
        window.location.reload();
      } else {
        await checkTerms(); // Update term status
      }
    } catch (error) {
      toast.error("Failed to delete term");
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
      setDeletingTermIndex(null);
      setDeletingTermData(null); // Clear the deleting term data
    }
  };

  const handleDeleteClick = (index) => {
    // Store the term data before opening the modal
    setDeletingTermData(terms[index]);
    setDeletingTermIndex(index);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setDeletingTermIndex(null);
    setDeletingTermData(null); // Clear the deleting term data
  };

  const RenderTermForm = ({ children }) => {
    return (
      <Modal
        isOpen={isTermModalOpen}
        onClose={handleCloseModal}
        headingText={
          termFormMode === "edit" ? "Edit Term Details" : "Add New Term"
        }
      >
        {children}
      </Modal>
    );
  };

  return (
    <>
      <Card sx={{ my: { xs: 1, sm: 2 } }}>
        <MDBox
          p={{ xs: 2, sm: 3 }}
          sx={{
            minHeight: { xs: "calc(100vh - 8rem)", sm: "calc(100vh - 13rem)" },
            height: "100%",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <MDBox mb={{ xs: 2, sm: 3 }}>
            <MDTypography variant="h5" fontWeight="bold">
              Academic Terms
            </MDTypography>
            <MDTypography variant="body2" color="text">
              Manage your institute's academic terms - Click on a term card to
              edit
            </MDTypography>
          </MDBox>

          {!hasCurrentTerm && !termsLoading && (
            <Alert
              severity="error"
              sx={{
                mb: 2,
                "& .MuiAlert-message": {
                  width: "100%",
                },
              }}
            >
              <MDTypography variant="body2" fontWeight="medium">
                No active term found! A current term is necessary to access all
                features of the application. Please ensure one of your terms
                range includes today's date.
              </MDTypography>
            </Alert>
          )}

          <MDBox
            display="flex"
            flexDirection="column"
            alignItems="center"
            flex={1}
            width="100%"
          >
            {isLoading || termsLoading ? (
              <MDBox
                width="100%"
                height={{ xs: "250px", sm: "300px" }}
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Loader message="Loading Terms..." />
              </MDBox>
            ) : (
              <>
                {terms.length === 0 ? (
                  <MDBox
                    display="flex"
                    flexDirection="column"
                    alignItems="center"
                    justifyContent="center"
                    minHeight={{ xs: "40vh", sm: "50vh" }}
                    px={{ xs: 2, sm: 3 }}
                  >
                    <HourglassEmptyIcon
                      color="info"
                      sx={{
                        fontSize: {
                          xs: "2.5rem !important",
                          xl: "3rem !important",
                        },
                      }}
                    />

                    <MDTypography
                      variant="h5"
                      color="dark"
                      textAlign="center"
                      mt={2}
                      sx={{
                        fontSize: {
                          xs: "1.25rem",
                          sm: "1.5rem",
                        },
                      }}
                    >
                      No Terms Found
                    </MDTypography>
                    <MDTypography
                      variant="body1"
                      color="secondary"
                      textAlign="center"
                      mt={1}
                      mb={3}
                      sx={{
                        fontSize: {
                          xs: "0.875rem",
                          sm: "1rem",
                        },
                        px: { xs: 1, sm: 4, md: 6 },
                      }}
                    >
                      Start by creating your first term to manage academic
                      sessions
                    </MDTypography>
                    <MDButton
                      variant="gradient"
                      color="info"
                      onClick={handleAddMoreTerm}
                      sx={{
                        width: { xs: "8rem", sm: "10rem" },
                        fontSize: { xs: "0.75rem", sm: "0.875rem" },
                      }}
                    >
                      Add Term
                    </MDButton>
                  </MDBox>
                ) : (
                  <MDBox
                    width="100%"
                    height="100%"
                    display="flex"
                    flexDirection="column"
                    flex={1}
                  >
                    <TermList
                      terms={terms}
                      handleAddMoreTerm={handleAddMoreTerm}
                      handleDeleteTerm={handleDeleteClick}
                      isAdvancedSettings={true}
                      hasCurrentTerm={hasCurrentTerm}
                    />
                  </MDBox>
                )}
              </>
            )}
          </MDBox>
        </MDBox>
      </Card>

      <RenderTermForm>
        <TermForm
          onSubmit={handleAddNewTermSubmit}
          termFormMode={termFormMode}
          initialValues={
            editingTermIndex !== null
              ? {
                  name: terms[editingTermIndex].name,
                  startDate: dayjs(terms[editingTermIndex].startDate),
                  endDate: dayjs(terms[editingTermIndex].endDate),
                }
              : undefined
          }
        />
      </RenderTermForm>

      <DeleteTermModal
        open={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={handleDeleteTerm}
        isLoading={isDeleting}
        termName={deletingTermData?.name || ""}
        startDate={deletingTermData?.startDate || ""}
        endDate={deletingTermData?.endDate || ""}
      />
    </>
  );
}

export default AcademicTermsSettings;

