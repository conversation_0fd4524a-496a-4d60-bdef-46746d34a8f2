import { useFormikContext, useField } from "formik";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { TextField } from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";

const FormikDatePicker = ({ name, label, ...props }) => {
  const { setFieldValue, setFieldTouched, validateField } = useFormikContext();
  const [field, meta] = useField(name);

  const handleChange = (newValue) => {
    setFieldValue(name, newValue);
    setFieldTouched(name, true, false);
    validateField(name);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        {...props}
        value={field.value || null}
        onChange={handleChange}
        format="DD/MM/YYYY"
        slots={{
          textField: (params) => (
            <TextField
              {...params}
              label={label}
              error={meta.touched && Boolean(meta.error)}
              helperText={meta.touched && meta.error}
              fullWidth
              required
            />
          ),
        }}
      />
    </LocalizationProvider>
  );
};

export default FormikDatePicker;
