// validations.js
import * as Yup from "yup";
import form from "./form";

const {
  formField: { name, startDate, endDate },
} = form;

const validations = [
  Yup.object().shape({
    [name.name]: Yup.string()
      .max(30, "Term Name must be at most 30 characters")
      .required(name.errorMsg)
      .trim(),
    [startDate.name]: Yup.date()
      .nullable()
      .required(startDate.errorMsg)
      .typeError("Invalid date format. Please select a valid start date"),
    [endDate.name]: Yup.date()
      .nullable()
      .required(endDate.errorMsg)
      .typeError("Invalid date format. Please select a valid end date")
      .min(
        Yup.ref(startDate.name),
        "End Date cannot be earlier than the Start Date"
      ),
  }),
];

export default validations;
