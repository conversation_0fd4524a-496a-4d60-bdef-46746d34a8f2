import React from "react";
import { Card, Grid, Skeleton } from "@mui/material";
import MDBox from "components/atoms/MDBox";

const TableSkeleton = () => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <MDBox pt={3} px={3}>
            <Skeleton animation="wave" width={200} height={30} />
          </MDBox>
          <MDBox py={1} px={3}>
            {[1, 2, 3, 4, 5].map((item) => (
              <MDBox
                key={item}
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                py={2}
              >
                <Skeleton animation="wave" width="15%" height={20} />
                <Skeleton animation="wave" width="15%" height={20} />
                <Skeleton animation="wave" width="15%" height={20} />
                <Skeleton animation="wave" width="15%" height={20} />
                <Skeleton animation="wave" width="15%" height={20} />
              </MDBox>
            ))}
          </MDBox>
        </Card>
      </Grid>
    </Grid>
  );
};

export default TableSkeleton;