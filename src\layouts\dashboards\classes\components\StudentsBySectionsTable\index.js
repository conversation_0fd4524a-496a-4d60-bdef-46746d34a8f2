import React, { useContext, useEffect, useState } from "react";
import Grid from "@mui/material/Grid";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import SalesTable from "examples/Tables/SalesTable";
import BarChartsSkeleton from "../Loaders/BarChartsSkeleton";
import studentBySectionsData from "../../data/studentsBySectionsTableData";

const StudentsBySectionsTable = ({ grade }) => {
  const { apiService } = useContext(ApiServiceContext);
  const [studentsData, setStudentsData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const studentsBySections = await apiService.getClassStudentsBySections(
          grade
        );
        setStudentsData(studentsBySections);
      } catch (err) {
        toast.error("Error loading students by sections data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [apiService, grade]);

  if (loading) return <BarChartsSkeleton />;
  if (!studentsData) return null;

  return (
    <SalesTable
      title="Students by Sections"
      rows={studentBySectionsData(studentsData) || []}
    />
  );
};

export default StudentsBySectionsTable;

