import { useState, useEffect, useContext } from "react";
import { Route, Navigate, useLocation } from "react-router-dom";
import { ThemeProvider } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import Sidenav from "examples/Sidenav";
import Configurator from "examples/Configurator";
import theme from "assets/theme";
import themeDark from "assets/theme-dark";
import routes from "routes";
import {
  useMaterialUIController,
  setMiniSidenav,
  ApiServiceContext,
} from "context";
import { useTermStatus } from "context/useTermStatus"; // Updated import
import brandWhite from "assets/images/logo-eddyowl-white.png";
import brandDark from "assets/images/logo-eddyowl.png";
import { Authorized } from "layouts/authentication/Authorized";
import { useAuth0 } from "@auth0/auth0-react";
import { SignIn } from "layouts/authentication/SignIn";
import { FaroRoutes } from "@grafana/faro-react";
import ErrorPage from "layouts/pages/error/ErrorPage";
import CreateNewInstitute from "layouts/pages/create-new-institute/CreateNewInstitute";
import SwitchInstitute from "layouts/pages/switch-institute/SwitchInstitute";
import AdvancedSettings from "layouts/dashboards/settings/AdvancedSettings";
import USER_ROLES from "utils/helperFunctions/USER_ROLES";
import StudentAssignments from "layouts/student/StudentAssignments";
import Institute from "layouts/dashboards/institute";
import StudentResponses from "layouts/dashboards/assignments/submission";

export default function App() {
  const { apiService, handleLogin, loading } = useContext(ApiServiceContext);
  const { user, isLoading } = useAuth0();
  const { hasCurrentTerm, termsLoading } = useTermStatus(); // Added hook
  const [filteredRoutes, setFilteredRoutes] = useState([]);
  const [routesReady, setRoutesReady] = useState(false);

  const [controller, dispatch] = useMaterialUIController();
  const {
    miniSidenav,
    layout,
    sidenavColor,
    transparentSidenav,
    whiteSidenav,
    darkMode,
  } = controller;
  const [onMouseEnter, setOnMouseEnter] = useState(false);
  const { pathname } = useLocation();

  const Redirect = ({ to }) => {
    useEffect(() => {
      window.location.href = to;
    }, [to]);
    return null;
  };

  useEffect(() => {
    if (
      !loading &&
      !apiService &&
      window.location.pathname !== "/authentication/logout"
    ) {
      handleLogin(window.location.href);
    }
  }, [apiService, loading, handleLogin]);

  useEffect(() => {
    if (apiService) {
      const filterRoutes = (allRoutes) => {
        return allRoutes
          .filter((route) => {
            // Always show routes with requiresNoId
            if (route.requiresNoId) {
              return true;
            }

            // If no instituteId, only allow specific routes
            if (!apiService || !apiService.instituteId) {
              return false;
            }

            // If no current term, only allow specific routes
            if (
              (apiService?.role === USER_ROLES.INSTRUCTOR_ROLE ||
                apiService?.role === USER_ROLES.ADMIN_ROLE) &&
              !hasCurrentTerm &&
              !termsLoading
            ) {
              const allowedPaths = [
                "/settings/general",
                "/settings/advanced",
                "/switch-institute",
                "/create-new-institute",
                "/authentication/logout",
              ];

              if (route.collapse) {
                const hasAllowedChild = route.collapse.some((child) =>
                  allowedPaths.includes(child.route)
                );
                if (hasAllowedChild) return true;
              }

              return allowedPaths.includes(route.route);
            }

            // If student role, only allow student-specific routes
            if (apiService?.role === USER_ROLES.STUDENT_ROLE) {
              const studentAllowedPaths = [
                "/student/assignments",
                "/student/submission/:assignmentId",
                "/switch-institute",
                "/authentication/logout",
                "/error",

              ];

              if (route.collapse) {
                const hasAllowedChild = route.collapse.some((child) =>
                  studentAllowedPaths.includes(child.route)
                );
                if (hasAllowedChild) return true;
              }

              return studentAllowedPaths.includes(route.route);
            }

            return true;
          })
          .map((route) => {
            if (route.collapse) {
              const filteredCollapse = filterRoutes(route.collapse);
              if (filteredCollapse.length > 0) {
                return {
                  ...route,
                  collapse: filteredCollapse,
                };
              }
              return route;
            }
            return route;
          });
      };

      const filtered = filterRoutes(routes(user, apiService?.role));
      setFilteredRoutes(filtered);
      setRoutesReady(true);
    }
  }, [apiService, user, hasCurrentTerm, termsLoading]);

  const handleOnMouseEnter = () => {
    if (miniSidenav && !onMouseEnter) {
      setMiniSidenav(dispatch, false);
      setOnMouseEnter(true);
    }
  };

  const handleOnMouseLeave = () => {
    if (onMouseEnter) {
      setMiniSidenav(dispatch, true);
      setOnMouseEnter(false);
    }
  };

  useEffect(() => {
    document.documentElement.scrollTop = 0;
    document.scrollingElement.scrollTop = 0;
  }, [pathname]);

  const getRoutes = (allRoutes) =>
    allRoutes.map((route) => {
      if (route.collapse) {
        return getRoutes(route.collapse);
      }

      if (route.route) {
        return (
          <Route
            exact
            path={route.route}
            element={route.component}
            key={route.key}
          />
        );
      }

      return null;
    });

  const configsButton = <></>;

  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Special case for create-new-institute route
  if ((!apiService || !routesReady) && pathname !== "/create-new-institute") {
    return <div>Loading...</div>;
  }

  return (
    <ThemeProvider
      theme={darkMode ? themeDark : theme}
      sx={{ padding: "10px" }}
    >
      <CssBaseline />
      {layout === "dashboard" &&
        pathname !== "/switch-institute" &&
        pathname !== "/create-new-institute" && (
          <>
            <Sidenav
              color={sidenavColor}
              brand={
                (transparentSidenav && !darkMode) || whiteSidenav
                  ? brandDark
                  : brandWhite
              }
              routes={filteredRoutes}
              onMouseEnter={handleOnMouseEnter}
              onMouseLeave={handleOnMouseLeave}
            />
            <Configurator />
            {configsButton}
          </>
        )}
      {layout === "vr" && <Configurator />}

      <FaroRoutes>
        {getRoutes(filteredRoutes)}
        {(() => {
          // No instituteId case
          if (!apiService?.instituteId) {
            return (
              <>
                <Route path="/" element={<Redirect to="https://eddyowl.com" />} />
                <Route path="/create-new-institute" element={<CreateNewInstitute />} />
                <Route path="/authentication/sign-in/authorized" element={<Authorized />} />
                <Route path="/authentication/sign-in/basic" element={<SignIn />} />
                <Route path="*" element={<Navigate to="/create-new-institute" />} />
              </>
            );
          }

          // No current term case
          if (!hasCurrentTerm) {
            return (
              <>
                <Route path="/settings/advanced" element={<AdvancedSettings />} />
                <Route path="/switch-institute" element={<SwitchInstitute />} />
                <Route path="/create-new-institute" element={<CreateNewInstitute />} />
                <Route path="*" element={<Navigate to="/settings/advanced" />} />
              </>
            );
          }

          // Student role case
          if (apiService?.role === USER_ROLES.STUDENT_ROLE) {
            return (
              <>
                <Route path="/" element={<Redirect to="https://eddyowl.com" />} />
                <Route path="/error" element={<ErrorPage />} />
                <Route path="/switch-institute" element={<SwitchInstitute />} />
                <Route path="/student/assignments" element={<StudentAssignments />} />
                <Route path="/student/submission/:assignmentId" element={<StudentResponses />} />
                <Route path="/authentication/sign-in/authorized" element={<Authorized />} />
                <Route path="/authentication/sign-in/basic" element={<SignIn />} />
                <Route path="*" element={<Navigate to="/student/assignments" />} />
              </>
            );
          }

          // Default case (other roles) Admin and Instructor
          return (
            <>
              <Route path="/" element={<Institute />} />
              <Route path="/error" element={<ErrorPage />} />
              <Route path="/switch-institute" element={<SwitchInstitute />} />
              <Route path="/create-new-institute" element={<CreateNewInstitute />} />
              <Route path="/settings/advanced" element={<AdvancedSettings />} />
              <Route path="/authentication/sign-in/authorized" element={<Authorized />} />
              <Route path="/authentication/sign-in/basic" element={<SignIn />} />
              <Route path="*" element={<Navigate to="/error" />} />
            </>
          );
        })()}
      </FaroRoutes>
    </ThemeProvider>
  );
}
