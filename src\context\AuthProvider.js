import React from "react";
import { Auth0Provider } from "@auth0/auth0-react";

const onRedirectCallback = (appState) => {
  if (appState && appState.redirectUrl) {
    sessionStorage.setItem("redirect_uri", appState.redirectUrl);
  }
};

const AuthProvider = ({ children }) => (
  <Auth0Provider
    domain={process.env.REACT_APP_AUTH0_DOMAIN}
    clientId={process.env.REACT_APP_AUTH0_CLIENT_ID}
    authorizationParams={{
      redirect_uri: process.env.REACT_APP_REDIRECT_URI,
      audience: process.env.REACT_APP_AUTH0_AUDIENCE,
    }}
    useRefreshTokens={true}
    cacheLocation="localstorage"
    onRedirectCallback={onRedirectCallback}
    scope="openid profile email"
  >
    {children}
  </Auth0Provider>
);

export default AuthProvider;
