/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

/**
  This file is used for controlling the global states of the components,
  you can customize the states for the different components here.
*/

import {
  createContext,
  useContext,
  useMemo,
  useReducer,
  useEffect,
  useState,
} from "react";

import { useAuth0 } from "@auth0/auth0-react";
import ApiService from "service/apiService";
// prop-types is a library for typechecking of props

import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";

// The Material Dashboard 2 PRO React main context
const MaterialUI = createContext();

// Setting custom name for the context which is visible on react dev tools
MaterialUI.displayName = "MaterialUIContext";

// Material Dashboard 2 PRO React reducer
function reducer(state, action) {
  switch (action.type) {
    case "MINI_SIDENAV": {
      return { ...state, miniSidenav: action.value };
    }
    case "TRANSPARENT_SIDENAV": {
      return { ...state, transparentSidenav: action.value };
    }
    case "WHITE_SIDENAV": {
      return { ...state, whiteSidenav: action.value };
    }
    case "SIDENAV_COLOR": {
      return { ...state, sidenavColor: action.value };
    }
    case "TRANSPARENT_NAVBAR": {
      return { ...state, transparentNavbar: action.value };
    }
    case "FIXED_NAVBAR": {
      return { ...state, fixedNavbar: action.value };
    }
    case "OPEN_CONFIGURATOR": {
      return { ...state, openConfigurator: action.value };
    }
    case "DIRECTION": {
      return { ...state, direction: action.value };
    }
    case "LAYOUT": {
      return { ...state, layout: action.value };
    }
    case "DARKMODE": {
      return { ...state, darkMode: action.value };
    }
    default: {
      throw new Error(`Unhandled action type: ${action.type}`);
    }
  }
}

// Material Dashboard 2 PRO React context provider
function MaterialUIControllerProvider({ children }) {
  const initialState = {
    miniSidenav: false,
    transparentSidenav: false,
    whiteSidenav: false,
    sidenavColor: "info",
    transparentNavbar: true,
    fixedNavbar: true,
    openConfigurator: false,
    direction: "ltr",
    layout: "dashboard",
    darkMode: false,
  };

  const [controller, dispatch] = useReducer(reducer, initialState);

  const value = useMemo(() => [controller, dispatch], [controller, dispatch]);

  return <MaterialUI.Provider value={value}>{children}</MaterialUI.Provider>;
}

// Material Dashboard 2 PRO React custom hook for using context
function useMaterialUIController() {
  const context = useContext(MaterialUI);

  if (!context) {
    throw new Error(
      "useMaterialUIController should be used inside the MaterialUIControllerProvider."
    );
  }

  return context;
}

// Typechecking props for the MaterialUIControllerProvider
MaterialUIControllerProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

// Context module functions
const setMiniSidenav = (dispatch, value) =>
  dispatch({ type: "MINI_SIDENAV", value });
const setTransparentSidenav = (dispatch, value) =>
  dispatch({ type: "TRANSPARENT_SIDENAV", value });
const setWhiteSidenav = (dispatch, value) =>
  dispatch({ type: "WHITE_SIDENAV", value });
const setSidenavColor = (dispatch, value) =>
  dispatch({ type: "SIDENAV_COLOR", value });
const setTransparentNavbar = (dispatch, value) =>
  dispatch({ type: "TRANSPARENT_NAVBAR", value });
const setFixedNavbar = (dispatch, value) =>
  dispatch({ type: "FIXED_NAVBAR", value });
const setOpenConfigurator = (dispatch, value) =>
  dispatch({ type: "OPEN_CONFIGURATOR", value });
const setDirection = (dispatch, value) =>
  dispatch({ type: "DIRECTION", value });
const setLayout = (dispatch, value) => dispatch({ type: "LAYOUT", value });
const setDarkMode = (dispatch, value) => dispatch({ type: "DARKMODE", value });

//Custom functionalities

const AuthWrapper = ({ children }) => {
  const { isAuthenticated, isLoading, loginWithRedirect } = useAuth0();
  const [authChecked, setAuthChecked] = useState(false);

  useEffect(() => {
    const checkAuthentication = async () => {
      if (!isLoading) {
        setAuthChecked(true);
        if (!isAuthenticated) {
          const currentUrl = window.location.href;
          await loginWithRedirect({
            appState: { redirectUrl: currentUrl },
          });
        }
      }
    };

    checkAuthentication();
  }, [isLoading, isAuthenticated, loginWithRedirect]);

  if (!authChecked || isLoading) {
    return <div>Loading...</div>;
  }

  return <>{children}</>;
};
// Create context with initial value (null)
export const ApiServiceContext = createContext(null);

export const ApiServiceProvider = ({ children }) => {
  const {
    getAccessTokenSilently,
    getIdTokenClaims,
    isAuthenticated,
    loginWithRedirect,
    logout,
  } = useAuth0();
  const [apiService, setApiService] = useState(null);
  const [loading, setLoading] = useState(true);

  const navigate = useNavigate();

  const handleError = (errorStatus, errorMessage) => {
    // to redirect user to login page if access token expires...
    if (errorStatus === 401 && errorMessage === "Invalid Token") {
      handleLogin(window.location.href);
      return;
    }

    navigate("/error", { state: { errorStatus, errorMessage } });
  };

  const redirectToSwitchInstitute = (userInfo) => {
    navigate("/switch-institute", { state: { userInfo } });
  };

  const handleLogin = async (redirectUrl) => {
    try {
      await loginWithRedirect({
        appState: {
          redirectUrl: redirectUrl,
        },
      }); // Redirect to login page
    } catch (error) {}
  };

  const handleLogout = () => {
    // Remove selected institute ID from localStorage on logout
    if (sessionStorage.getItem("SELECTED_INSTITUTE_ID")) {
      sessionStorage.removeItem("SELECTED_INSTITUTE_ID");
    }

    // Logout and reset ApiService
    setApiService(null);
  };

  useEffect(() => {
    const initializeApiService = async () => {
      if (isAuthenticated) {
        try {
          setLoading(true); // Set loading to true before initialization
          const accessToken = await getAccessTokenSilently();
          const idToken = await getIdTokenClaims();
          const idTokenVal = idToken.__raw;
          const service = new ApiService(
            accessToken,
            process.env.REACT_APP_BASE_URL,
            handleError,
            redirectToSwitchInstitute,
            idToken.email // Pass the email from Auth0
          );
          await service.initInstituteId();
          setApiService(service); // Set the initialized service
        } catch (error) {
          // Handle error scenario, maybe redirect to login page
        } finally {
          setLoading(false); // Always set loading to false after initialization or error
        }
      } else {
        setApiService(null); // Reset ApiService if not authenticated
        setLoading(false); // Set loading to false if not authenticated
      }
    };

    initializeApiService(); // Call initialization function on mount and auth changes
  }, [getAccessTokenSilently, getIdTokenClaims, isAuthenticated]);

  return (
    <ApiServiceContext.Provider
      value={{ apiService, loading, handleLogin, handleLogout }}
    >
      {loading ? <div>Loading...</div> : children}
    </ApiServiceContext.Provider>
  );
};

export {
  MaterialUIControllerProvider,
  useMaterialUIController,
  setMiniSidenav,
  setTransparentSidenav,
  setWhiteSidenav,
  setSidenavColor,
  setTransparentNavbar,
  setFixedNavbar,
  setOpenConfigurator,
  setDirection,
  setLayout,
  setDarkMode,
  AuthWrapper,
};
