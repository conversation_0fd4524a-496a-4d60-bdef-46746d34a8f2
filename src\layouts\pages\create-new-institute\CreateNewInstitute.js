import React, { useState, useContext } from "react";
import {
  <PERSON>,
  Grid,
  Stepper,
  Step,
  Step<PERSON><PERSON><PERSON>,
  ThemeProvider,
  createTheme,
} from "@mui/material";
import MDBox from "components/atoms/MDBox";
import { toast } from "react-toastify";
import { ApiServiceContext } from "context";
import FullPageHeader from "components/atoms/FullPageHeader/FullPageHeader";
import TermForm from "components/organisms/TermForm/TermForm";
import MDButton from "components/atoms/MDButton";
import { useNavigate } from "react-router-dom";
import AddInstituteForm from "components/organisms/AddInsituteForm/AddInstituteForm";
import TermList from "components/organisms/TermList/TermList";
import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import Loader from "components/atoms/Loader/Loader";
import isRangeOverlapping from "utils/helperFunctions/isRangeOverlapping";
import CreateInstituteModal from "components/molecules/CreateInstituteModal/CreateInstituteModal";

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

function CreateNewInstitute() {
  const navigate = useNavigate();
  const { apiService } = useContext(ApiServiceContext);

  const [activeStep, setActiveStep] = useState(0);
  const [submitLoader, setSubmitLoader] = useState(false);
  const [instituteFormData, setInstituteFormData] = useState({
    name: "",
    addressOne: "",
    addressTwo: "",
    city: "",
    state: "",
    pincode: "",
  });
  const [terms, setTerms] = useState([]);
  const [showTermForm, setShowTermForm] = useState(true);
  const [termFormMode, setTermFormMode] = useState("view");
  const [editingTermIndex, setEditingTermIndex] = useState(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const steps = ["Institute Information", "Add Term"];

  const handleInstituteSubmit = (values, actions) => {
    // Store institute form data
    setInstituteFormData({
      name: values.name,
      addressOne: values.addressOne || "",
      addressTwo: values.addressTwo || "",
      city: values.city || "",
      state: values.state || "",
      pincode: values.pincode || "",
    });

    // Move to next step
    if (terms.length > 0) {
      setActiveStep(1);
      setShowTermForm(false);
    } else {
      setActiveStep(1);
      setShowTermForm(true);
      setTermFormMode("view");
    }
    actions.setSubmitting(false);
  };

  const handleAddTerm = (termData, actions) => {
    const startDate = termData.startDate;
    const endDate = termData.endDate;

    const { hasOverlap, overlappingTermNames } = isRangeOverlapping(
      startDate,
      endDate,
      terms,
      editingTermIndex
    );

    if (hasOverlap) {
      toast.error(
        `The selected date range overlaps with: ${overlappingTermNames.join(
          ", "
        )}`
      );
      actions.setSubmitting(false);
      return;
    }

    const newTerm = {
      name: termData.name,
      startDate: termData.startDate,
      endDate: termData.endDate,
    };

    if (editingTermIndex !== null) {
      // Update existing term
      const updatedTerms = [...terms];
      updatedTerms[editingTermIndex] = newTerm;
      setTerms(updatedTerms);
      setEditingTermIndex(null);
    } else {
      // Add new term
      setTerms([...terms, newTerm]);
    }

    // Hide the form and reset mode
    setShowTermForm(false);
    setTermFormMode("view");
    actions.setSubmitting(false);
    actions.resetForm();
  };

  const handleEditTerm = (index) => {
    // Set the editing mode and show the form with existing term data
    setEditingTermIndex(index);
    setShowTermForm(true);
    setTermFormMode("edit");
  };

  const handleDeleteTerm = (index) => {
    const updatedTerms = terms.filter((_, i) => i !== index);
    setTerms(updatedTerms);

    if (updatedTerms.length === 0) {
      setTermFormMode("view");
      setShowTermForm(true);
    }
  };

  const handleCreateInstitute = async () => {
    // Validate institute information
    if (!instituteFormData.name) {
      toast.error("Institute information is missing");
      return;
    }

    // Validate terms
    if (terms.length === 0) {
      toast.error("Please add at least one term");
      return;
    }

    const today = dayjs();
    const hasCurrentTerm = terms.some((term) => {
      const termStart = term.startDate;
      const termEnd = term.endDate;
      return today.isSameOrAfter(termStart) && today.isSameOrBefore(termEnd);
    });

    if (!hasCurrentTerm) {
      toast.error("At least one term must be active today.");
      return;
    }

    // Open the confirmation modal instead of proceeding directly
    setIsCreateModalOpen(true);
  };

  const handleConfirmCreate = async () => {
    setSubmitLoader(true);

    try {
      // Prepare institute data
      const instituteData = {
        name: instituteFormData.name,
        program: "CBSE",
        address: {
          addressOne: instituteFormData.addressOne,
          addressTwo: instituteFormData.addressTwo,
          city: instituteFormData.city,
          state: instituteFormData.state,
          pincode: instituteFormData.pincode,
        },
        terms: terms.map((term) => ({
          name: term.name,
          startDate: new Date(term.startDate).toISOString(),
          endDate: new Date(term.endDate).toISOString(),
        })),
      };

      // Submit institute data
      const res = await apiService.addInstitute(instituteData);
      toast.success("Institute Created successfully!");

      if (apiService.instituteId === "" || !apiService.instituteId) {
        navigate(0);
      } else {
        apiService.setInstituteId(res.id);
        navigate("/");
      }
    } catch (err) {
      toast.error("An error occurred. Please try again.");
    } finally {
      setSubmitLoader(false);
      setIsCreateModalOpen(false);
    }
  };

  const handleBack = () => {
    if (showTermForm && terms.length > 0) {
      // If in term form, go back to terms list
      setShowTermForm(false);
      setEditingTermIndex(null);
    } else {
      // If in terms list, go back to institute form
      setActiveStep(0);
    }
  };

  const handleAddMoreTerm = () => {
    setShowTermForm(true);
    setTermFormMode("more");
    setEditingTermIndex(null);
  };

  const theme = createTheme({
    components: {
      MuiStepper: {
        styleOverrides: {
          root: {
            backgroundColor: "white",
          },
        },
      },
      MuiStepIcon: {
        styleOverrides: {
          root: {
            color: "grey",
            "&.Mui-active": {
              color: "#00C6E1",
            },
            "&.Mui-completed": {
              color: "#000",
            },
          },
        },
      },
    },
  });

  return (
    <MDBox
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        minHeight: "100vh",
        width: "100%",
        position: "relative",
        p: 4,
        background: "linear-gradient(0deg, #00C6E1 0%, #006D7D 100%)",
      }}
    >
      <FullPageHeader />

      <Card
        sx={{
          mt: { xs: 8, sm: 2 },
          p: 3,
          width: { xs: "100%", lg: "75%" },
          maxWidth: "800px",
          boxShadow: 4,
          borderRadius: "12px",
          backgroundColor: "#ffffff",
        }}
      >
        <MDBox mb={2}>
          <ThemeProvider theme={theme}>
            <Stepper activeStep={activeStep} alternativeLabel>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </ThemeProvider>
        </MDBox>

        <Grid container>
          <Grid item xs={12}>
            {activeStep === 0 ? (
              <AddInstituteForm
                onSubmit={handleInstituteSubmit}
                initialValues={instituteFormData}
              />
            ) : (
              <MDBox>
                {/* Term Form or Add More Term Form */}
                {showTermForm ? (
                  <TermForm
                    onSubmit={handleAddTerm}
                    termFormMode={termFormMode}
                    initialValues={
                      editingTermIndex !== null
                        ? {
                            name: terms[editingTermIndex].name,
                            startDate: dayjs(terms[editingTermIndex].startDate),
                            endDate: dayjs(terms[editingTermIndex].endDate),
                          }
                        : undefined
                    }
                  />
                ) : (
                  <TermList
                    terms={terms}
                    handleEditTerm={handleEditTerm}
                    handleAddMoreTerm={handleAddMoreTerm}
                    handleDeleteTerm={handleDeleteTerm}
                  />
                )}

                {/* Navigation Buttons */}
                <MDBox
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                  sx={{ mt: 4 }}
                >
                  <MDButton
                    variant="outlined"
                    color="dark"
                    onClick={handleBack}
                  >
                    Back
                  </MDButton>

                  {!showTermForm && (
                    <MDBox
                      display="flex"
                      justifyContent="center"
                      alignItems="center"
                      gap={2}
                    >
                      <MDButton
                        variant="gradient"
                        color="dark"
                        onClick={handleCreateInstitute}
                        disabled={terms.length === 0 || submitLoader}
                        sx={{ width: "10rem" }}
                      >
                        Create Institute
                      </MDButton>
                    </MDBox>
                  )}
                </MDBox>
              </MDBox>
            )}
          </Grid>
        </Grid>
      </Card>
      <CreateInstituteModal
        isCreateModalOpen={isCreateModalOpen}
        setIsCreateModalOpen={setIsCreateModalOpen}
        submitLoader={submitLoader}
        handleCreateInstitute={handleConfirmCreate}
      />
    </MDBox>
  );
}

export default CreateNewInstitute;
