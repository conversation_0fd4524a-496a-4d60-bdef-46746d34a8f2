import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import Loader from "components/atoms/Loader/Loader";
import MDButton from "components/atoms/MDButton";
import checkIsCurrentTerm from "utils/helperFunctions/checkIsCurrentTerm";

function DeleteTermModal({
  open,
  onClose,
  onConfirm,
  isLoading,
  termName,
  startDate,
  endDate,
}) {
  const isCurrentTerm = checkIsCurrentTerm(startDate, endDate);

  return (
    <Dialog
      open={open}
      onClose={() => !isLoading && onClose()}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <MDTypography variant="h5" color="error">
          Delete Term
        </MDTypography>
      </DialogTitle>
      <DialogContent>
        {isCurrentTerm && (
          <MDBox mb={2}>
            <MDTypography variant="body2" fontWeight="bold" color="warning">
              CAUTION: You are deleting the current active term. A new term must
              be set up to resume operations.
            </MDTypography>
          </MDBox>
        )}

        <MDTypography variant="h6" color="dark">
          Deleting "{termName}" will permanently remove:
        </MDTypography>

        <MDBox mt={2}>
          <ul style={{ listStyleType: "disc", paddingLeft: "20px" }}>
            <li>
              <MDTypography variant="body3" color="dark">
                All assignments and submissions records
              </MDTypography>
            </li>
            <li>
              <MDTypography variant="body3" color="dark">
                Student enrollments and progress data
              </MDTypography>
            </li>
          </ul>
        </MDBox>

        <MDBox mt={2}>
          <MDTypography variant="body2" fontWeight="bold" color="error">
            This action cannot be undone.
          </MDTypography>
        </MDBox>
      </DialogContent>
      <DialogActions>
        <MDBox
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          width="100%"
          px={0.5}
        >
          <MDButton
            variant="outlined"
            color="dark"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </MDButton>
          <MDButton
            variant="gradient"
            color="error"
            onClick={onConfirm}
            disabled={isLoading}
            sx={{ width: "10rem" }}
          >
            {isLoading ? <Loader size={20} /> : "Delete Term"}
          </MDButton>
        </MDBox>
      </DialogActions>
    </Dialog>
  );
}

export default DeleteTermModal;

