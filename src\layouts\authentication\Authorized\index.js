import React, { useEffect, useContext, useState } from "react";
import { Navigate } from "react-router-dom";
import { ApiServiceContext } from "context";

export const Authorized = () => {
  const { apiService, loading } = useContext(ApiServiceContext);
  const [redirectPath, setRedirectPath] = useState(null);

  const handleRedirectUri = (redirect_uri) => {
    try {
      const url = new URL(redirect_uri);
      const path = url.pathname;
      if (path === "/error") {
        setRedirectPath("/");
      } else {
        setRedirectPath(path && path !== "/" ? path : "/");
      }

      sessionStorage.removeItem("redirect_uri");
    } catch (error) {
      setRedirectPath("/");
    }
  };

  useEffect(() => {
    if (loading || !apiService) return;

    const checkAuthorization = async () => {
      try {
        const redirect_uri = sessionStorage.getItem("redirect_uri");

        if (!apiService.instituteId || apiService.instituteId === "") {
          setRedirectPath("/create-new-institute");
          return;
        }

        if (redirect_uri) {
          handleRedirectUri(redirect_uri);
        } else {
          setRedirectPath("/");
        }
      } catch (error) {
        setRedirectPath("/create-new-institute");
      }
    };

    checkAuthorization();
  }, [apiService, loading]);

  if (redirectPath === null || loading) {
    return <div>Checking authorization...</div>;
  }

  return <Navigate to={redirectPath} />;
};
