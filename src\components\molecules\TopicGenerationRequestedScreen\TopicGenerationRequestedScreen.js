import React from "react";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import HourglassTopIcon from "@mui/icons-material/HourglassTop";

function TopicGenerationRequestedScreen() {
  return (
    <MDBox
      display="flex"
      flexDirection="column"
      alignItems="center"
      gap={3}
      height="calc(100vh - 20rem)"
      justifyContent="center"
    >
      <HourglassTopIcon
        color="info"
        sx={{
          fontSize: {
            xs: "3rem !important",
            xl: "4rem !important",
          },
        }}
      />
      <MDTypography variant="h4" fontWeight="bold" textAlign="center">
        Topics Generation in Progress
      </MDTypography>
      <MDTypography
        variant="h5"
        fontWeight="regular"
        textAlign="center"
        sx={{
          fontSize: {
            xs: "1rem",
            md: "1.3rem",
          },
        }}
      >
        Topics generation in progress. Please check the assignment later for
        generated topics.
      </MDTypography>
    </MDBox>
  );
}

export default TopicGenerationRequestedScreen;

