import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import Loader from "components/atoms/Loader/Loader";
import HC_more from "highcharts/highcharts-more";

HC_more(Highcharts);

// Function to calculate average scores for unique topics within chapters
const getChapterWisePerformanceSpiderChartOptions = (subject, subjectData) => {
  if (!subject || !subjectData) return null;

  const topicScores = new Map();

  // Collect scores for each unique topic from all chapters and tests
  subjectData.forEach((test) => {
    test.chapters.forEach(({ topics }) => {
      topics.forEach(({ topic, score }) => {
        let topicData = topicScores.get(topic) || { total: 0, count: 0 };
        topicData.total += score;
        topicData.count += 1;
        topicScores.set(topic, topicData);
      });
    });
  });

  // Calculate average scores
  const finalDataSeries = Array.from(
    topicScores,
    ([topic, { total, count }]) => ({
      name: topic,
      y: count > 0 ? total / count : 0,
    })
  );

  // Set up the chart options
  const options = {
    chart: {
      type: "line",
      polar: true,
      height: "500px",
      spacing: [30, 20, 0, 20],
      borderRadius: 25,
    },
    title: {
      text: `Topic Wise Performance in ${subject}`,
      align: "left",
    },
    legend: {
      enabled: false,
    },
    pane: {
      size: "80%",
    },
    tooltip: {
      pointFormat: "{point.name}: <b>{point.y:.2f}</b>",
    },
    xAxis: {
      categories: finalDataSeries.map((data) => data.name),
      tickmarkPlacement: "on",
      lineWidth: 0,
    },
    yAxis: {
      min: 0,
      max: 10,
    },
    series: [
      {
        name: "Performance",
        data: finalDataSeries,
        colorByPoint: true,
      },
    ],
    credits: {
      enabled: false,
    },
  };

  return options;
};

export const DownloadStudentPerformanceSpiderChart = ({ subject, data }) => {
  const [chartOptions, setChartOptions] = useState(null);

  useEffect(() => {
    // Filter the data to get the selected subject's tests
    const subjectStats = data.stats.find((item) => item.subject === subject);

    if (subjectStats) {
      const options = getChapterWisePerformanceSpiderChartOptions(
        subject,
        subjectStats.tests
      );
      setChartOptions(options);
    }
  }, [subject, data]);

  return (
    <div>
      {chartOptions ? (
        <HighchartsReact highcharts={Highcharts} options={chartOptions} />
      ) : (
        <Loader />
      )}
    </div>
  );
};
