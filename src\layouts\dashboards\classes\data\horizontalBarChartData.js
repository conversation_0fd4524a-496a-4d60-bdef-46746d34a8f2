/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

const colorMap = {
  "Section A": "info",
  "Section B": "primary",
  "Section C": "dark",
  "Section D": "secondary",
};

const horizontalBarChartData = (data) => {
  const assessmentData = {
    labels: data.sections || [],
    datasets: [
      {
        label: "Assignments",
        backgroundColors: [],
        data: data.assessments || [],
      },
    ],
  };
  assessmentData.datasets.backgroundColors = assessmentData.labels.map(
    (label) => colorMap[label] || "defaultColor"
  );
  return assessmentData;
};

export default horizontalBarChartData;
