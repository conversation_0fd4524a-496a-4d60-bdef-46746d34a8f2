import * as Yup from "yup";

import checkout from "./form";

const {
  formField: { startDate, endDate, subjectList },
} = checkout;

const validations = [
  Yup.object().shape({
    [startDate.name]: Yup.date()
      .nullable()
      .required(startDate.errorMsg)
      .typeError("Invalid date format. Please select a valid start date"),

    [endDate.name]: Yup.date()
      .nullable()
      .required(endDate.errorMsg)
      .typeError("Invalid date format. Please select a valid end date")
      .min(
        Yup.ref(startDate.name),
        "End Date cannot be earlier than the Start Date"
      ),

    [subjectList.name]: Yup.array()
      .of(Yup.string().required("Subject cannot be empty"))
      .min(1, subjectList.errorMsg)
      .required("Please select at least one subject."),
  }),
];

export default validations;
