const formField = {
  firstName: {
    name: "firstName",
    label: "First Name",
    type: "text",
    placeholder: "<PERSON>",
    errorMsg: "First name is required",
  },
  lastName: {
    name: "lastName",
    label: "Last Name",
    type: "text",
    placeholder: "Do<PERSON>",
    errorMsg: "Last name is required",
  },
  studentId: {
    name: "studentId",
    label: "Student ID",
    type: "text",
    placeholder: "S12345",
    errorMsg: "Student ID is required",
  },
  rollNumber: {
    name: "rollNumber",
    label: "Roll Number",
    type: "number",
    placeholder: "1",
    errorMsg: "Roll number is required",
  },
  class: {
    name: "class",
    label: "Class",
    type: "number",
    placeholder: "10",
    errorMsg: "Class is required",
  },
  section: {
    name: "section",
    label: "Section",
    type: "text",
    placeholder: "A",
    errorMsg: "Section is required",
  },
  email: {
    name: "email",
    label: "Email",
    type: "email",
    placeholder: "<EMAIL>",
  },
};

const form = {
  formId: "student-form",
  formField,
};

export default form;
