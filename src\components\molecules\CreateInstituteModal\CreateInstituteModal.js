import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import Loader from "components/atoms/Loader/Loader";
import MDButton from "components/atoms/MDButton";

function CreateInstituteModal({
  isCreateModalOpen,
  setIsCreateModalOpen,
  submitLoader,
  handleCreateInstitute,
}) {
  return (
    <Dialog
      open={isCreateModalOpen}
      onClose={() => !submitLoader && setIsCreateModalOpen(false)}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <MDTypography variant="h5" color="dark">
          Confirm Institute Creation
        </MDTypography>
      </DialogTitle>
      <DialogContent>
        <MDTypography variant="h6" color="dark">
          Please review the following information before creating the institute:
        </MDTypography>
        <MDBox mt={2}>
          <ul style={{ listStyleType: "disc", paddingLeft: "20px" }}>
            <li>
              <MDTypography variant="body2" color="dark">
                Term Range cannot be changed once institute is created.
              </MDTypography>
            </li>
            <li>
              <MDTypography variant="body2" color="dark">
                A current term is required to access all features.
              </MDTypography>
            </li>
          </ul>
        </MDBox>
      </DialogContent>
      <DialogActions>
        <MDBox
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          width="100%"
          px={0.5}
        >
          <MDButton
            variant="outlined"
            color="dark"
            onClick={() => setIsCreateModalOpen(false)}
            disabled={submitLoader}
          >
            Cancel
          </MDButton>
          <MDButton
            variant="gradient"
            color="info"
            onClick={handleCreateInstitute}
            disabled={submitLoader}
            sx={{ width: "10rem" }}
          >
            {submitLoader ? <Loader size={20} /> : "Create Institute"}
          </MDButton>
        </MDBox>
      </DialogActions>
    </Dialog>
  );
}

export default CreateInstituteModal;

