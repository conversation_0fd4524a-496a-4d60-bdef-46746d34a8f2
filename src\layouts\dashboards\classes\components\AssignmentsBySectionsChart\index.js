import React, { useContext, useEffect, useState } from "react";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import HorizontalBarChart from "examples/Charts/BarCharts/HorizontalBarChart";
import BarChartsSkeleton from "../Loaders/BarChartsSkeleton";
import horizontalBarChartData from "../../data/horizontalBarChartData";

const AssignmentsBySectionsChart = ({ grade }) => {
  const { apiService } = useContext(ApiServiceContext);
  const [assessmentData, setAssessmentData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const assessmentsBySections =
          await apiService.getClassAssessmentsBySections(grade);
        setAssessmentData(assessmentsBySections);
      } catch (err) {
        toast.error("Error loading assignments by sections data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [apiService, grade]);

  if (loading) return <BarChartsSkeleton />;
  if (!assessmentData) return null;

  return (
    <HorizontalBarChart
      title="Assignment by Sections"
      chart={horizontalBarChartData(assessmentData)}
    />
  );
};

export default AssignmentsBySectionsChart;

