import React, { useContext, useEffect, useState } from "react";
import { Grid } from "@mui/material";
import { ApiServiceContext } from "context";
import ReportsBarChart from "examples/Charts/BarCharts/ReportsBarChart";
import reportsBarChartData from "../../data/reportsBarChartData";
import { toast } from "react-toastify";
import PopOverChartSkeleton from "../../SkeletonLoaders/PopOverChartSkeleton";

const ReportsBarChartWrapper = () => {
  const { apiService } = useContext(ApiServiceContext);

  const [weeklyAssessments, setWeeklyAssessments] = useState(null);

  useEffect(() => {
    if (weeklyAssessments) return;
    apiService
      .getWeeklyAssessmentsForInstitute()
      .then((data) => setWeeklyAssessments(data?.weeklyData))
      .catch(() => {
        return toast.error("Error loading weekly assessments");
      });
  }, []);
  return (
    <Grid item xs={12} md={6}>
      {weeklyAssessments ? (
        <ReportsBarChart
          color="info"
          title="Weekly Assignments Graded"
          description="Last Week Analysis"
          chart={reportsBarChartData(weeklyAssessments)}
        />
      ) : (
        <PopOverChartSkeleton />
      )}
    </Grid>
  );
};

export default ReportsBarChartWrapper;
