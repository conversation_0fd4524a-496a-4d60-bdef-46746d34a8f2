import React, { useContext, useEffect, useState } from "react";
import Grid from "@mui/material/Grid";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import AssessmentsBySubjectChart from "../AssessmentsBySubjectChart";
import ChartsSkeleton from "../Loaders/ChartsSkeleton";

const AssessmentsBySubjectChartWrapper = ({ grade }) => {
  const { apiService } = useContext(ApiServiceContext);
  const [assessmentData, setAssessmentData] = useState(null);
  const [loading, setLoading] = useState(true);

  const colors = [
    "primary",
    "secondary",
    "info",
    "success",
    "warning",
    "error",
    "light",
    "dark",
  ];
  const processColorMap = (data) => {
    const subjectColorMap = {};
    data.subjects.forEach((subject, index) => {
      subjectColorMap[subject] = colors[index % colors.length];
    });
    return subjectColorMap;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await apiService.getClassAssessmentsBySubject(grade);

        // Filter out zero values while maintaining index correlation
        const filteredData = {
          subjects: data.subjects.filter(
            (_, index) => data.assessments[index] > 0
          ),
          assessments: data.assessments.filter((value) => value > 0),
        };

        const colorMap = filteredData?.subjects
          ? processColorMap(filteredData)
          : null;
        setAssessmentData({ data: filteredData, colorMap });
      } catch (err) {
        toast.error("Error loading assessment data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [apiService, grade]);

  if (loading)
    return (
      <Grid item xs={12} md={6}>
        <ChartsSkeleton />
      </Grid>
    );

  if (!assessmentData?.data) return null;

  return (
    <Grid item xs={12} md={6}>
      <AssessmentsBySubjectChart
        data={assessmentData.data}
        colorMap={assessmentData.colorMap}
      />
    </Grid>
  );
};

export default AssessmentsBySubjectChartWrapper;
