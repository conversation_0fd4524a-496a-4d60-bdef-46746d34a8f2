import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

const isRangeOverlapping = (start, end, terms, editingTermIndex = null) => {
  const overlappingTerms = terms.filter((term, index) => {
    // Skip the term being edited
    if (editingTermIndex === index) {
      return false;
    }

    const termStart = term.startDate;
    const termEnd = term.endDate;

    return (
      (start.isSameOrAfter(termStart) && start.isSameOrBefore(termEnd)) ||
      (end.isSameOrAfter(termStart) && end.isSameOrBefore(termEnd)) ||
      (start.isBefore(termStart) && end.isAfter(termEnd))
    );
  });

  return {
    hasOverlap: overlappingTerms.length > 0,
    overlappingTermNames: overlappingTerms.map((term) => term.name),
  };
};

export default isRangeOverlapping;
