import { useContext, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import MDBox from "components/atoms/MDBox";
import Card from "@mui/material/Card";
import MDTypography from "components/atoms/MDTypography";
import { Grid, Icon, Tooltip } from "@mui/material";
import sampleStudentData from "./data/sampleData";
import ComplexStatisticsCard from "examples/Cards/StatisticsCards/ComplexStatisticsCard";
import defaultLineChartData from "layouts/dashboards/classes/data/defaultLineChartData";
import MDBadgeDot from "components/atoms/MDBadgeDot";
import MDButton from "components/atoms/MDButton";
import Loader from "components/atoms/Loader/Loader";
import { ApiServiceContext } from "context";
import CustomLineChart from "examples/Charts/LineCharts/CustomLineChart";
import StudentPerformanceSpiderChart from "components/molecules/StudentsCharts/StudentPerformanceSpiderChart";
import { DownloadStudentReportModal } from "components/molecules/DownloadStudentReportModal";

const StudentDashboard = () => {
  const { studentId } = useParams();
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);
  useEffect(() => {
    if (!loading && !apiService) {
      handleLogin(window.location.href);
    }
  }, [apiService, loading]);

  const [studentStats, setStudentStats] = useState({});
  const [isLoading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [isStudentReportModalOpen, setIsStudentReportModalOpen] =
    useState(false);

  useEffect(() => {
    const studentStats = {};

    const fetchStudentStats = async () => {
      try {
        let result = await apiService.getStudent(studentId);
        studentStats.studentDetails = result;

        result = await apiService.getStudentOverallStats(studentId);
        studentStats.overallStats = result;

        // result = await apiService.getStudentMonthlyAssignment(studentId);
        // studentStats.studentMonthlyAssignment = result;

        // 

        setStudentStats(studentStats);
        // 

        setLoading(false);
      } catch (err) {
        setError(err);
      }
    };
    fetchStudentStats();
  }, [apiService, studentId]);

  if (isLoading) {
    return <Loader fullScreen message="Don't Reload Page" />;
  }

  return (
    <DashboardLayout>
      <DashboardNavbar breadCrumbText={studentId} />
      <MDBox py={3}>
        {/* Upper Section */}
        <Grid container spacing={4} px={2}>
          {/* Info Card - Left Side */}
          <Grid item xs={12} md={6} mb={2}>
            <Card
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-evenly",
                padding: "20px",
                height: "100%",
                width: "100%",
              }}
            >
              <MDBox display="flex" alignItems="center" gap={2} width="100%">
                <MDTypography variant="h6" fontWeight="regular">
                  Student Name:
                </MDTypography>
                <MDTypography variant="h5" fontWeight="medium">
                  {`${studentStats?.studentDetails?.firstName} ${studentStats?.studentDetails?.lastName}`}
                </MDTypography>
              </MDBox>
              <MDBox display="flex" alignItems="center" gap={2} width="100%">
                <MDTypography variant="h6" fontWeight="regular">
                  Roll No:
                </MDTypography>
                <MDTypography variant="h5" fontWeight="medium">
                  {studentStats?.studentDetails?.rollNumber}
                </MDTypography>
              </MDBox>

              <MDBox display="flex" alignItems="center" gap={2} width="100%">
                <MDTypography variant="h6" fontWeight="regular">
                  Grade:
                </MDTypography>
                <MDTypography variant="h5" fontWeight="medium">
                  {`${studentStats?.studentDetails?.class} (${studentStats?.studentDetails?.section})`}
                </MDTypography>
              </MDBox>
            </Card>
          </Grid>

          {/* Stats - Right Side */}
          <Grid item xs={12} md={6} mb={2}>
            <Grid container spacing={3}>
              {/* First Card */}
              <Grid item xs={12} xl={6}>
                <MDBox>
                  <ComplexStatisticsCard
                    color="dark"
                    icon="assignment"
                    title="Total Assignment Solved"
                    count={
                      studentStats?.overallStats?.totalAssignmentsSolved || "0"
                    }
                    sx={{ height: "150px" }}
                  />
                </MDBox>
              </Grid>

              {/* Second Card */}
              <Grid item xs={12} xl={6}>
                <MDBox>
                  <ComplexStatisticsCard
                    color="info"
                    icon="person"
                    title="Average Student Performance"
                    count={
                      studentStats?.overallStats?.averageStudentPerformance
                        ? `${studentStats?.overallStats?.averageStudentPerformance}%`
                        : "0"
                    }
                    sx={{ height: "150px" }}
                  />
                </MDBox>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        {/* Mid Section */}
        <MDBox mt={5} mb={4}>
          <Grid container spacing={3}>
            {/* Student Performance Chart - Left Side */}
            <Grid item xs={12} md={6}>
              <StudentPerformanceSpiderChart />
            </Grid>

            {/* Line Chart - Right Side */}
            <Grid item xs={12} md={6}>
              <CustomLineChart
                isBig
                title="Monthly Avarage Score By Subject"
                description={
                  <MDBox display="flex" justifyContent="space-between">
                    <MDBox display="flex" ml={-1}>
                      {defaultLineChartData(
                        sampleStudentData.monthlyAssignmentScoreBySubject,
                        sampleStudentData.monthlyAssignmentScoreBySubject
                          .colorMap
                      ).datasets.map((dataset, index) => (
                        <MDBadgeDot
                          key={index}
                          color={dataset.color}
                          size="sm"
                          badgeContent={dataset.label}
                        />
                      ))}
                    </MDBox>
                    <MDBox mt={-4} mr={-1} position="absolute" right="1.5rem">
                      <Tooltip
                        title="See the monthly avarage performance of student by subject"
                        placement="left"
                        arrow
                      >
                        <MDButton
                          variant="outlined"
                          color="secondary"
                          size="small"
                          circular
                          iconOnly
                        >
                          <Icon>priority_high</Icon>
                        </MDButton>
                      </Tooltip>
                    </MDBox>
                  </MDBox>
                }
                chart={defaultLineChartData(
                  sampleStudentData.monthlyAssignmentScoreBySubject,
                  sampleStudentData.monthlyAssignmentScoreBySubject.colorMap
                )}
              />
            </Grid>
          </Grid>
        </MDBox>
      </MDBox>

      <DownloadStudentReportModal
        isStudentReportModalOpen={isStudentReportModalOpen}
        setIsStudentReportModalOpen={setIsStudentReportModalOpen}
      />
      {/* download Student Report Button */}
      {!isStudentReportModalOpen && (
        <MDButton
          variant="gradient"
          color="primary"
          sx={{
            position: "absolute",
            top: { xs: 85, sm: 38 },
            right: { xs: 50, sm: 120 },
            p: "8px 12px",
            zIndex: 9999,
          }}
          onClick={() => setIsStudentReportModalOpen(true)}
        >
          <Icon>file_download</Icon>
          <MDTypography variant="h6" color="white" ml={1} pb={"0.8px"}>
            Student Report
          </MDTypography>
        </MDButton>
      )}
    </DashboardLayout>
  );
};
export default StudentDashboard;
