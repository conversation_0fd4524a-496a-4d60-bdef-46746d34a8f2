import { useEffect, useState, useContext } from "react";
import { ApiServiceContext } from "context";
import MDBox from "components/atoms/MDBox";
import React from "react";
import FullPageHeader from "components/atoms/FullPageHeader/FullPageHeader";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import USER_ROLES from "utils/helperFunctions/USER_ROLES";
import SwitchInstituteCard from "components/molecules/SwitchInstituteCard/SwitchInstituteCard";
import SelectRoleCard from "components/molecules/SelectRoleCard/SelectRoleCard";

function SwitchInstitute() {
  const navigate = useNavigate();
  const { apiService } = useContext(ApiServiceContext);

  const [institutesList, setInstitutesList] = useState([]);
  const [institutesLoading, setInstitutesLoading] = useState(false);
  const [userRoles, setUserRoles] = useState([]);
  const [showRoleSelection, setShowRoleSelection] = useState(true);
  const [hasStudentRole, setHasStudentRole] = useState(false);

  const fetchInstitutesData = async () => {
    setInstitutesLoading(true);
    try {
      const { roles } = await apiService.getUserRoles();
      setUserRoles(roles);

      if (roles.length === 0) {
        setInstitutesList([]);
        return;
      }

      // Set hasStudentRole based on roles
      setHasStudentRole(
        roles.some((role) => role.role === USER_ROLES.STUDENT_ROLE)
      );

      const filteredRoles = roles.filter(
        (role) => role.role !== USER_ROLES.STUDENT_ROLE
      );

      if (filteredRoles.length === 0) {
        setInstitutesList([]);
        return;
      }

      const institutesPromises = filteredRoles.map(async (role) => {
        try {
          return await apiService.getInstitute(role.instituteId);
        } catch (error) {
          return null;
        }
      });

      const institutesData = await Promise.all(institutesPromises);

      const validInstitutes = institutesData.map((institute) => ({
        ...institute,
        role: roles.find((role) => role.instituteId === institute.id)?.role,
      }));
      setInstitutesList(validInstitutes);
    } catch (err) {
      toast.error("Error fetching institute details");
      setInstitutesList([]);
    } finally {
      setInstitutesLoading(false);
    }
  };

  useEffect(() => {
    fetchInstitutesData();
  }, []);

  const handleContinueAsStudent = () => {
    const studentRole = userRoles.find(
      (role) => role.role === USER_ROLES.STUDENT_ROLE
    );

    if (studentRole) {
      apiService.setInstituteId(studentRole.instituteId);
      apiService.setUserRole(studentRole.role);
      navigate("/student/assignments");
    } else {
      toast.error("Student role not found");
    }
  };

  const handleContinueAsInstructor = () => {
    setShowRoleSelection(false);
  };

  const handleCreateNewInstitute = () => {
    apiService.setInstituteId("");
    apiService.setUserRole(USER_ROLES.INSTITUTE_ROLE);
    navigate("/create-new-institute");
  };

  return (
    <MDBox
      className="flex flex-col sm:justify-center h-screen w-screen"
      sx={{
        background: "linear-gradient(0deg, #00C6E1 0%, #006D7D 100%)",
      }}
    >
      <FullPageHeader />

      {hasStudentRole && showRoleSelection ? (
        <SelectRoleCard
          handleContinueAsStudent={handleContinueAsStudent}
          handleContinueAsInstructor={handleContinueAsInstructor}
          userRoles={userRoles}
        />
      ) : (
        <SwitchInstituteCard
          institutesLoading={institutesLoading}
          institutesList={institutesList}
          handleCreateNewInstitute={handleCreateNewInstitute}
          hasStudentRole={hasStudentRole}
          onChangeRole={() => setShowRoleSelection(true)}
        />
      )}
    </MDBox>
  );
}

export default SwitchInstitute;
