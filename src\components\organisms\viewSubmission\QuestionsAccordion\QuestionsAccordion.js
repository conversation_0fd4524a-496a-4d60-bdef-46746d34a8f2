import React from "react";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import Card from "@mui/material/Card";
import MDTypography from "components/atoms/MDTypography";
import HandwrittenLayout from "../HandwrittenLayout/HandwrittenLayout";
import FormField from "components/atoms/FormField";
import MDBox from "components/atoms/MDBox";
import { useNavigate, useParams } from "react-router-dom";
import renderOutOfScore from "utils/helperFunctions/renderOutOfScore";

const QuestionsAccordion = ({
  index,
  expanded,
  handleAccordionChange,
  response,
  editMode,
  getFieldProps,
  showRubric,
}) => {
  const { assignmentId } = useParams();
  const navigate = useNavigate();
  return (
    <Accordion
      disableGutters
      TransitionProps={{ unmountOnExit: true }}
      key={index}
      expanded={expanded === index}
      onChange={handleAccordionChange(index)}
      sx={{
        marginTop: "30px",
        borderRadius: "12px",
        overflow: "hidden !important",
        boxShadow: 2,
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls={`panel${index}-content`}
        id={`panel${index}-header`}
      >
        <MDBox
          display="flex !important"
          alignItems="center !important"
          justifyContent="space-between !important"
          width="100%"
        >
          <MDTypography variant="h6">
            Question {response.questionNumber}
          </MDTypography>

          {/* Display this text only when the accordion is closed */}
          {expanded !== index && (
            <MDTypography variant="h6" mr={3}>
              {renderOutOfScore(response?.score, response?.questionScore)}
            </MDTypography>
          )}
        </MDBox>
      </AccordionSummary>
      <AccordionDetails>
        <MDBox
          gap={3}
          display="flex"
          flexDirection={{
            xs: "column",
            lg: "row",
          }}
          alignItems="start"
        >
          {/* left section - question details */}
          <Grid
            container
            spacing={2}
            width={{
              xs: "100%",
              lg: "60%",
            }}
            pb={2}
          >
            {editMode ? (
              <>
                <Grid item xs={12} sm={3} mb={3}>
                  <FormField
                    type="number"
                    step="0.01"
                    label="Achieved Score"
                    max={response.questionScore}
                    {...getFieldProps(`StudentResponseList.${index}.score`)}
                    disabled={!editMode}
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormField
                    type="number"
                    label="Question Score"
                    {...getFieldProps(
                      `StudentResponseList.${index}.questionScore`
                    )}
                    disabled={true}
                  />
                </Grid>
              </>
            ) : (
              <Grid item xs={12} pr={4}>
                <MDBox
                  width="100%"
                  display="flex"
                  justifyContent="flex-end"
                  mt={-7}
                >
                  {renderOutOfScore(
                    response?.score,
                    response?.questionScore,
                    true
                  )}
                </MDBox>
              </Grid>
            )}
            <Grid item xs={12} mt={-4}>
              <MDTypography
                pl={3}
                variant="h5"
                fontWeight="regular"
                fontSize={"1.05rem"}
              >
                {response?.question}
              </MDTypography>
            </Grid>

            <Grid item xs={12}>
              <HandwrittenLayout
                heading={"Responses"}
                content={response?.studentResponse || "No Response"}
              />
            </Grid>
            {editMode ? (
              <Grid item xs={12}>
                <FormField
                  type="text"
                  label="Feedback"
                  {...getFieldProps(`StudentResponseList.${index}.feedback`)}
                  multiline={true}
                  disabled={!editMode}
                />
              </Grid>
            ) : (
              <Grid item xs={12}>
                <HandwrittenLayout
                  heading={"Feedback"}
                  content={response?.feedback || "No Feedback"}
                />
              </Grid>
            )}
          </Grid>
          {/* right section - rubric & topics */}
          <MDBox
            width={{
              xs: "100%",
              lg: "40%",
            }}
            mt={{
              xs: 6,
              lg: 0,
            }}
          >
            {showRubric && (
              <>
                {editMode ? (
                  <Grid item xs={12}>
                    <FormField
                      type="text"
                      label="Rubric"
                      {...getFieldProps(
                        `StudentResponseList.${index}.questionRubric`
                      )}
                      multiline={true}
                      disabled={true}
                    />
                  </Grid>
                ) : (
                  <Grid
                    item
                    xs={12}
                    pl={1}
                    mt={{
                      xs: -2,
                      md: -7,
                    }}
                  >
                    <MDTypography variant="h6" fontWeight="medium">
                      Rubric
                    </MDTypography>
                    <MDTypography
                      mt={1.5}
                      pl={1}
                      variant="h5"
                      fontWeight="regular"
                      fontSize={"1.05rem"}
                    >
                      {response?.questionRubric}
                    </MDTypography>
                  </Grid>
                )}
              </>
            )}
            <MDBox
              mt={
                !showRubric
                  ? {
                      xs: -12,
                      lg: -9,
                    }
                  : "unset"
              }
            >
              <MDTypography variant="h6" width="38%" p={1} mt={2} mb={1}>
                Topics
              </MDTypography>
              {response.topics && response.topics.length > 0 ? (
                response.topics.map((topicGroup, index) => {
                  const topicList = topicGroup?.topics?.map((tp) => tp);
                  return (
                    <MDBox
                      key={index}
                      sx={{
                        marginBottom: "20px",
                        mx: "8px",
                        bgcolor: "#f1f2f6",
                        borderRadius: "0px !important",
                        overflow: "hidden",
                        "& .MuiPaper-root:first-of-type": {
                          borderRadius: "0px !important",
                        },
                        "& .MuiPaper-root:last-of-type": {
                          borderRadius: "0px !important",
                        },
                      }}
                    >
                      <Card
                        sx={{
                          padding: "12px",
                          bgcolor: "#f1f2f6",
                        }}
                      >
                        <MDBox
                          display="flex"
                          flexDirection="column"
                          alignItems="start"
                          justifyContent="space-between"
                          sx={{
                            width: "100%",
                            height: "100%",
                            borderRadius: "0px !important",
                          }}
                        >
                          {/* card top side */}
                          <MDBox
                            display="flex"
                            flexDirection={{
                              xs: "column",
                              lg: "row",
                            }}
                            alignItems="start"
                            gap={1}
                            p={1}
                          >
                            <MDTypography variant="h6" fontWeight="regular">
                              {`Chapter${
                                response.topics?.length > 1
                                  ? " " + (index + 1)
                                  : ""
                              }: `}
                            </MDTypography>
                            <MDTypography variant="h6" fontWeight="medium">
                              {topicGroup.chapter}
                            </MDTypography>
                          </MDBox>

                          {/* card bottom side */}
                          <MDBox
                            width={"100%"}
                            sx={{
                              borderRadius: "10px !important",
                              "& .MuiPaper-root:first-of-type": {
                                borderRadius: "10px !important",
                              },
                              "& .MuiPaper-root:last-of-type": {
                                borderRadius: "10px !important",
                              },
                            }}
                          >
                            <Card>
                              <MDBox
                                sx={{
                                  overflow: "hidden",
                                  width: "100%",
                                  position: "relative",
                                }}
                              >
                                <Autocomplete
                                  multiple
                                  width="100%"
                                  options={topicList}
                                  getOptionLabel={(option) => option}
                                  value={topicList || []}
                                  className="assignment_dropdown"
                                  disabled={true}
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      variant="outlined"
                                      sx={{
                                        width: "100%",
                                        "& .MuiInputBase-root": {
                                          overflow: "hidden",
                                          whiteSpace: "nowrap",
                                          textOverflow: "ellipsis",
                                        },
                                      }}
                                    />
                                  )}
                                />
                              </MDBox>
                            </Card>
                          </MDBox>
                        </MDBox>
                      </Card>
                    </MDBox>
                  );
                })
              ) : (
                <MDBox
                  width="100%"
                  height="300px"
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                >
                  <MDTypography variant="h6" fontWeight="medium" align="center">
                    No topics found ! <br /> You can generate Topics in
                    <span
                      className="cursor-pointer pl-2 text-blue-600"
                      onClick={() =>
                        navigate(`/assignments/${assignmentId}?operation=view`)
                      }
                    >
                      View Assignment
                    </span>
                  </MDTypography>
                </MDBox>
              )}
            </MDBox>
          </MDBox>
        </MDBox>
      </AccordionDetails>
    </Accordion>
  );
};

export default QuestionsAccordion;
