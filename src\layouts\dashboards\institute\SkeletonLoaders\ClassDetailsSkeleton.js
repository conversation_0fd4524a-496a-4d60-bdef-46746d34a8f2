import React from "react";
import { Card, Skeleton } from "@mui/material";
import MDBox from "components/atoms/MDBox";

const ClassDetailsSkeleton = () => {
  return (
    <Card>
      <MDBox height={"32rem"}>
        <MDBox
          display="flex"
          flexDirection="column"
          alignItems="flex-start"
          justifyContent="space-between"
          pl={13}
          py={2}
          position="relative"
          height={80}
        >
          <MDBox
            bgColor="light"
            color="light"
            coloredShadow="light"
            borderRadius="xl"
            width="4.2rem"
            height="4.2rem"
            zIndex={1}
            sx={{
              position: "absolute",
              top: "-1rem",
              left: "1.3rem",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Skeleton animation="wave" width="60%" height="100%" />
          </MDBox>
          <Skeleton animation="wave" width={100} />
          <Skeleton animation="wave" width={250} />
        </MDBox>
        <MDBox px={4}>
          <Skeleton animation="wave" width="100%" height="29rem" />
        </MDBox>
      </MDBox>
    </Card>
  );
};

export default ClassDetailsSkeleton;

