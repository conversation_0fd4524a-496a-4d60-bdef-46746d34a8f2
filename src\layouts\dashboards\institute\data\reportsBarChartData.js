/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

const reportsBarChartData = (weeklyDataArray) => {
  const days = ["Sun", "Mon", "<PERSON>e", "Wed", "Thu", "Fri", "Sat"];
  const today = new Date();
  const labels = [];

  // Generate labels for the last 7 days (including today)
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    labels.push(days[date.getDay()]);
  }

  return {
    labels,
    datasets: { label: "Assignments", data: weeklyDataArray },
  };
};

export default reportsBarChartData;
